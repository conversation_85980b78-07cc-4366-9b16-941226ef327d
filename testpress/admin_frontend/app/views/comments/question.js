import View from 'views/view';
import reverse from 'lib/reverse';
import {getQueryVariable} from 'views/utils/functions';
import CKEditor from 'utils/editors/ckeditor';

import {QuestionModel} from 'models/questions';
import {QuestionExamsCollection} from 'models/questions';
import {ContentObjectCommentsBaseView, ContentObjectBaseEditView} from 'views/comments/base';

import ExamTitleTemplate from 'views/templates/comments/exam';
import QuestionTemplate from 'views/templates/questions/admin_comment_item';
import EditQuestionTemplate from 'views/templates/questions/admin_comment_edit_item';


var QuestionCommentsView = ContentObjectCommentsBaseView.extend({
  initContentObject: function() {
    this.content_object = new QuestionModel({
      id: this.comment.get('content_object')['id']
    });
  },

  initContentObjectPreviewView: function() {
    this.subviews['content_object_view'] = new QuestionPreviewView({
      model: this.content_object,
    });
  },

  initContentObjectEditView: function() {
    this.subviews['content_object_view'] = new QuestionEditView({
      model: this.content_object,
      editor_class: CKEditor
    });
  }
});


var QuestionPreviewView = View.extend({

  events: {
    'click .dropdown-toggle' : 'initExamsLoader',
    'click .exams-retry': 'reloadExams',
  },

  template: QuestionTemplate,

  exam_template: ExamTitleTemplate,

  initialize: function() {
    this.initLoadingProgress();
    this.initRetry();
    this.exams_collection = new QuestionExamsCollection([], {
      content_object: this.model
    });
    this.listenTo(this.exams_collection, 'errorOnLoadingExams', this.error);
  },

  initLoadingProgress: function() {
    this.progress = $('<div class="pagination-centered"><img src="/static/img/ajax-loader.gif"></div>');
  },

  initRetry: function() {
    this.retry = $('<div class="pagination-centered" ><div style="bottom: 12px; font-weight: bold; ' +
            'border-radius: 2px; color: rgb(34, 34, 34); background-color: rgb(249, 237, 190); ' +
            'padding: 1px 15px; left: 34%;">Unable to get comments.&nbsp;&nbsp;'+
            '<a style="text-decoration: underline; color: rgb(34, 34, 34);" href="#" '+
            'class="exams-retry">Try again</a></div></div>');
  },

  render: function() {
    _.defer(() => {
      window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub, this.el]);
    });
    this.$el.html(this.template(this.model.toJSON()));
    return this;
  },

  errorOnLoadingExams: function() {
    this.$el.find('#comments-exams-list').html(this.retry);
  },

  reloadExams: function(evt) {
    evt.stopPropagation();
    this.initExamsLoader();
  },

  initExamsLoader: function() {
    if (this.exams) {
      // Exam collection is already loaded, no need to load again
      return;
    }
    this.$el.find('#comments-exams-list').html(this.progress);
    this.fetchExams();
  },

  fetchExams: function(data) {
    this.exams_collection.fetch({
      data: data,
      remove: false,
      success: (collection, xhr, options) => {
        if (collection.next !== null) {
          var page = getQueryVariable(collection.next, 'page');
          this.fetchExams({'page': page});
        } else {
          this.exams = this.exams_collection.toJSON();
          this.exams_collection.reset();
          delete this.exams_collection;
          
          this.$el.find('#comments-exams-list').html("");
          _.each(this.exams, (exam) => {
            exam['url'] = reverse('exam_admin_url', { 'id': exam.id })
            this.$el.find('#comments-exams-list').append(this.exam_template(exam));
          });
        }
      },
    });
  },
});


var QuestionEditView = ContentObjectBaseEditView.extend({
  template: EditQuestionTemplate,

  initialize: function(options) {
    this.editors = {};
    this.editor_class = options.editor_class;
  },

  save: function() {
    var direction = null, direction_id = null;
    if (this.model.get('direction')) {
      direction = this.editors['direction'].getData();
      direction_id = this.model.get('direction').id;
    }
    var question_html = this.editors['question-html'].getData();
    var explanation_html = this.editors['explanation-html'].getData();
    var answers = _.map($('.item.option'), (value, key, list) => {
      var answer_id = parseInt($(value).find('[name="id"]').val(), 10);
      return {
        'id': answer_id,
        'text_html': this.editors[`text-html-${answer_id}`].getData(),
        'is_correct': $(value).find('[name="is_correct"]').is(':checked'),
      }
    });
    var data = {
      'question_html': question_html,
      'explanation_html': explanation_html,
      'answers': answers
    }

    if (this.model.get('direction')) {
      data['direction']= {'id': direction_id, 'html': direction}
    }

    this.model.save(data, {
      success: _.bind(this.saveComplete, this),
      patch: true
    });
  },

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    var elements = this.$el.find('.edit-mod-question, .edit-mod-option');
    _.each(elements, (selector) => {
      this.editors[selector.id] = new this.editor_class(selector);
      this.editors[selector.id].initialize();
      if (this.model.get('font')) {
        var question = this.model.toJSON();
        this.editors[selector.id].wrapWithCustomFont(question.font.name, question.font.path, '20px');
      }
    });
    return this;
  }
});


module.exports = {
  QuestionCommentsView: QuestionCommentsView,
  QuestionEditView: QuestionEditView
};
