import View from 'views/view';
import Comments from 'models/comments';
import MainTemplate from 'views/templates/comments/admin_view_thread';
import CommentsView from 'views/comments';


var ContentObjectCommentsBaseView = View.extend({
  events: {
    'click .admin-edit-content' : 'edit',
  },

  template: MainTemplate,

  initialize: function(options) {
    this.user = options.user;
    this.comment = options.comment;
    this.fetchContentObject();
  },

  edit: function(evt) {
    evt.preventDefault();
    this.$el.find('.admin-edit-content').hide();
    this.subviews['content_object_view'].remove();
    this.initContentObjectEditView();
    this.$el.find('.content-object').html(this.subviews['content_object_view'].render().el);
    return this;
  },

  fetchContentObject: function() {
    this.initContentObject();
    this.listenTo(this.content_object, 'show', this.rerender);
    this.content_object.fetch({
      success: _.bind(this.fetchCommentsThread, this)
    })
  },

  fetchCommentsThread: function() {
    this.render();
    var comments = new Comments.collection([], {
      content_object: this.content_object
    });
    this.comments = new CommentsView({
      collection: comments,
      parent_id: this.comment.get('id'),
      user: this.user,
      wysiwyg: true
    });
    this.subviews['comments'] = this.comments;
    this.$el.find('.discussions').html(this.comments.render().el);
  },

  rerender: function() {
    this.render();
    this.$el.find('.discussions').html(this.comments.render().el);
  },

  render: function() {
    this.$el.html(this.template(this.content_object.toJSON()));
    if (this.subviews.hasOwnProperty('content_object_view')) {
      this.subviews['content_object_view'].remove();
    }
    this.initContentObjectPreviewView();
    this.$el.find('.content-object').html(this.subviews['content_object_view'].render().el);
    return this;
  },
});


var ContentObjectBaseEditView = View.extend({
  events: {
    'click .save' : 'save',
    'click .cancel': 'cancel'
  },

  saveComplete: function() {
    this.model.trigger('show');
  },

  cancel: function(evt) {
    evt.preventDefault();
    this.model.trigger('show');
  },

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    return this;
  },

});


module.exports = {
    ContentObjectBaseEditView: ContentObjectBaseEditView,
    ContentObjectCommentsBaseView: ContentObjectCommentsBaseView,
};
