import View from 'views/view';
import {PostModel} from 'models/posts';
import {ContentObjectCommentsBaseView, ContentObjectBaseEditView} from 'views/comments/base';
import {addWysiwyg, CKEditorValueFor} from 'views/utils/functions';

import EditPostTemplate from 'views/templates/posts/admin_comment_edit_item';
import PostTemplate from 'views/templates/posts/admin_comment_item';


var PostCommentsView = ContentObjectCommentsBaseView.extend({
  initContentObject: function() {
    this.content_object = new PostModel({
      slug: this.comment.get('content_object')['slug']
    });
  },

  initContentObjectPreviewView: function() {
    this.subviews['content_object_view'] = new  PostPreviewView({
      model: this.content_object,
    });
  },

  initContentObjectEditView: function() {
    this.subviews['content_object_view'] = new PostEditView({
      model: this.content_object,
    });
  }
});


var PostPreviewView = View.extend({
  template: PostTemplate,

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    return this;
  }
});


var PostEditView = ContentObjectBaseEditView.extend({
  template: EditPostTemplate,

  save: function() {
    var title = this.$el.find('[name="title"]').val();
    var content_html = CKEditorValueFor('edit-post');
    this.model.set({
      'title': title,
      'content_html': content_html,
    });

    this.model.save([], {
      success: _.bind(this.saveComplete, this)
    });
  },

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    _.defer(() => {
        addWysiwyg('edit-post');
    });
    return this;
  }
});
  

module.exports = PostCommentsView;
