import View from 'views/view';
import {DiscussionModel} from 'models/discussions';
import {ContentObjectCommentsBaseView, ContentObjectBaseEditView} from 'views/comments/base';
import {addWysiwyg, CKEditorValueFor} from 'views/utils/functions';

import EditDiscussionTemplate from 'views/templates/discussions/admin_comment_edit_item';
import DiscussionTemplate from 'views/templates/discussions/admin_comment_item';


var DiscussionCommentsView = ContentObjectCommentsBaseView.extend({
  initContentObject: function() {
    this.content_object = new DiscussionModel({
      slug: this.comment.get('content_object')['slug']
    });
  },

  initContentObjectPreviewView: function() {
    this.subviews['content_object_view'] = new DiscussionPreviewView({
      model: this.content_object,
    });
  },

  initContentObjectEditView: function() {
    this.subviews['content_object_view'] = new DiscussionEditView({
      model: this.content_object,
    });
  }
});


var DiscussionPreviewView = View.extend({
  template: DiscussionTemplate,

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    return this;
  }
});


var DiscussionEditView = ContentObjectBaseEditView.extend({
  template: EditDiscussionTemplate,

  save: function() {
    var title = this.$el.find('[name="title"]').val();
    var content_html = CKEditorValueFor('edit-discussion');
    this.model.set({
      'title': title,
      'content_html': content_html,
    });

    this.model.save([], {
      success: _.bind(this.saveComplete, this)
    });
  },

  render: function() {
    this.$el.html(this.template(this.model.toJSON()));
    _.defer(() => {
        addWysiwyg('edit-discussion');
    });
    return this;
  }
});


module.exports = DiscussionCommentsView;