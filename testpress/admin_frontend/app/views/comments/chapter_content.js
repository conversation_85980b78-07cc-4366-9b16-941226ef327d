import View from 'views/view';
import {ContentObjectCommentsBaseView} from 'views/comments/base';
import {ChapterContentModel} from 'models/chapter_contents';
import ChapterContentTemplate from 'templates/chapter_contents/admin_comment_preview';


var ChapterContentCommentsView = ContentObjectCommentsBaseView.extend({
  initContentObject: function() {
    this.content_object = new ChapterContentModel({
      id: this.comment.get('content_object')['id']
    });
  },

  initContentObjectPreviewView: function() {
    this.subviews['content_object_view'] = new ChapterContentPreviewView({
      model: this.content_object,
    });
  },
});


var ChapterContentPreviewView = View.extend({
  template: ChapterContentTemplate,

  render: function() {
    let context = this.model.toJSON();
    context['embed_url']=this.model.get('embed_url')
    this.$el.html(this.template(context));
    return this;
  }
});


module.exports = ChapterContentCommentsView;
