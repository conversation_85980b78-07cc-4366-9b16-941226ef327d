import View from 'views/view';
import AuthUser from 'models/users';
import Comments from 'models/comments';
import ChapterContentCommentsView from 'views/comments/chapter_content';
import {QuestionCommentsView} from 'views/comments/question';
import PostCommentsView from 'views/comments/post';
import DiscussionCommentsView from 'views/comments/discussion';


var ChromeView = View.extend({
  fetch_auth_user: function(cb) {
    if (this.user.isNew()) {
      this.user.fetch({
        url: '/api/v2.3/me/',
        success: _.bind(cb, this)
      });
    } else {
      cb.call(this);
    }
  },

  initialize: function(options) {
    this.user = new AuthUser.model();
    this.fetch_auth_user(function() {
      this.comment = new Comments.model({
        id: options.comment_id
      });
      this.comment.fetch({
        success: _.bind(this.initCommentsView, this)
      });
    })
  },

  initCommentsView: function() {
    switch (this.comment.get('content_object')['type'].toLowerCase()) {
      case "question":
        this.detail_chrome_view = new QuestionCommentsView({
          user: this.user,
          comment: this.comment,
        });
        break;
  
      case "post":
        this.detail_chrome_view = new PostCommentsView({
          user: this.user,
          comment: this.comment,
        });
        break;

      case "chaptercontent":
        this.detail_chrome_view = new ChapterContentCommentsView({
          user: this.user,
          comment: this.comment,
        });
        break;

      case "forumthread":
        this.detail_chrome_view = new DiscussionCommentsView({
          user: this.user,
          comment: this.comment,
        });
        break;
    }
    this.subviews["detail_chrome_view"] = this.detail_chrome_view;
    this.render();
  },

  render: function() {
    this.$el.html(this.subviews["detail_chrome_view"].el);
    return this;
  },
});


module.exports = {
  AdminComments: ChromeView
};