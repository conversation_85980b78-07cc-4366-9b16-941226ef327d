import View from "../view";
import TableTemplate from "../../templates/subject-stats/table.jst";
import {SubjectStats as SubjectStatsModel, SubjectStatsCollection} from "../../models/subject-stats";
import {RowView} from './row';

const SAT_TEMPLATE = 16

const TableView = View.extend({
  template: TableTemplate,

  initialize: function(options) {
    this.collection = options.collection;
    this.exam_template_type = options.exam_template_type
    this.show_score_column= true
    this.showPartialCorrectStats = this.hasPartialCorrectStats();
  },

  hasPartialCorrectStats: function() {
    return Boolean(_.find(this.collection.models, (subject) => subject.get("partial_correct") != 0));
  },

  renderRow: function(subject) {
    let rowView = new RowView({
      model: subject,
      show_score_column: this.show_score_column,
      showPartialCorrectStats: this.showPartialCorrectStats,
    });
    this.$el.find('tbody').append(rowView.render().el);
    this.subviews[subject.get('id')] = rowView;
    this.listenTo(rowView, 'subject-clicked', this.subjectClicked);
  },

  renderTotalRow: function() {
    let questions = 0, correctQuestions = 0, incorrectQuestions = 0, unansweredQuestions = 0, score = 0;
    let partialCorrect = 0;

    this.collection.each((subject)=>{
      questions = questions + subject.get('total');
      correctQuestions = correctQuestions + subject.get('correct');
      incorrectQuestions = incorrectQuestions + subject.get('incorrect');
      unansweredQuestions = unansweredQuestions + subject.get('unanswered');
      score = parseFloat(score) + parseFloat(subject.get('score'));
      partialCorrect = partialCorrect + subject.get("partial_correct");
    });
    score = parseFloat(score).toFixed(2);

    let proxySubject = new SubjectStatsModel({
      name: "Total",
      leaf: true,
      total: questions,
      correct: correctQuestions,
      partial_correct: partialCorrect,
      incorrect: incorrectQuestions,
      unanswered: unansweredQuestions,
      score: score
    });
    this.renderRow(proxySubject);
  },

  subjectClicked: function(subject) {
    this.trigger("subject-clicked", subject);
  },

  render: function () {
    this.$el.html(
      this.template({ 
        show_score_column: this.show_score_column,
        showPartialCorrectStats: this.showPartialCorrectStats,
      })
    );
    this.collection.each((subject) => {
      this.renderRow(subject);
    });
    this.renderTotalRow();
    return this;
  },
});

export {TableView}
