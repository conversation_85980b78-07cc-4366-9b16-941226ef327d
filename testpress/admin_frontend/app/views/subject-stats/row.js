import View from "../view";
import RowTemplate from "../../templates/subject-stats/row.jst";

const RowView = View.extend({
  tagName: 'tr',

  initialize: function (options) {
    this.show_score_column = options.show_score_column;
    this.showPartialCorrectStats = options.showPartialCorrectStats;
  },

  template: RowTemplate,

  events: {
    'click .clickable': 'subjectClicked'
  },

  subjectClicked: function () {
    this.trigger('subject-clicked', this.model);
  },

  render: function () {
  const templateData = Object.assign({}, this.model.toJSON(), {
    show_score_column: this.show_score_column,
    showPartialCorrectStats: this.showPartialCorrectStats,
  });
  this.$el.html(this.template(templateData));
  return this;
}
});

export {RowView}
