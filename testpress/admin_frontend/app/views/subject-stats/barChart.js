import View from "../view";
import ChartTemplate from "../../templates/subject-stats/chart.jst";

const BarChartView = View.extend({
  template: ChartTemplate,

  initialize: function(options) {
    this.collection = options.collection;
    this.chartClass = options.chartClass;
    this.has_partial_correct_stats = this.hasPartialCorrectStats();
    this.initializeChart();
  },

  hasPartialCorrectStats: function() {
    return Boolean(_.find(this.collection.models, (subject) => subject.get("partial_correct") != 0));
  },

  initializeChart: function() {
    this.chart = new this.chartClass();
    this.chart.setBarsWithColors(this.getBarColors());
    this.collection.each((subject) => {
      this.chart.setDataSet(subject.get('name'), this.getChartDataForSubject(subject));
    });
  },

  getBarColors: function() {
    let colorsData = {
      "Total Questions": "rgb(109,165,255)",
      "Correct Attempts": "rgb(63,96,43)",
      "Partial Correct": "rgb(150,206,180)",
      "Incorrect Attempts": "rgb(255,111,105)",
      "Unattempted": "rgb(255,204,92)",
    }
    if (!this.has_partial_correct_stats) {
      delete colorsData["Partial Correct"];
    }
    return colorsData;
  },

  getChartDataForSubject: function(subject) {
    let chartData = {
      "Total Questions": subject.get('total'),
      "Correct Attempts": subject.get('correct'),
      "Partial Correct": subject.get("partial_correct"),
      "Incorrect Attempts": subject.get('incorrect'),
      "Unattempted": subject.get('unanswered'),
    };
    if (!this.has_partial_correct_stats) {
      delete chartData["Partial Correct"];
    }
    return chartData;
  },

  render: function () {
    this.$el.html(this.template());
    let $chart = this.$el.find('#chart')[0];
    let $legend = this.$el.find('.chart-legend')[0];
    this.chart.generateChart($chart);
    this.chart.generateLegend($legend);
    return this;
  }
});


export {BarChartView}
