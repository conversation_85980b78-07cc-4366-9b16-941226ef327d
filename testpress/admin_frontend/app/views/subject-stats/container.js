import View from "../view";
import SubjectsStatsTemplate from "../../templates/subject-stats/subjectStats.jst";
import {BarChart as BarChartClass} from "../../utils/charts/echart";
import {TableView} from './table';
import {BarChartView} from "./barChart";
import {SubjectStatsCollection} from "../../models/subject-stats";


const ContainerView = View.extend({
  template: SubjectsStatsTemplate,

  events: {
    'click .back': 'renderParentSubject'
  },

  initialize: function (options) {
    this.collection = options.collection;
    this.parentSubject = null;
    this.exam_template_type = options.exam_template_type
    let rootSubjects = this.collection.filter((subject) => {
      return subject.get("parent") === null;
    });
    this.proxyCollection = new SubjectStatsCollection(rootSubjects, {userexam_id: this.collection.userexam_id});
    this.listenTo(this.proxyCollection, 'change', this.render);
  },

  renderParentSubject: function(backButtonEvent) {
    let subjectId = backButtonEvent.target.id;
    let subject = this.collection.get(subjectId);

    if (subject.get('parent')) {
      this.parentSubject = this.collection.get(subject.get('parent'));
    } else {
      this.parentSubject = null;
    }

    let childSubjectsOfParentSubject = this.collection.filter((subject) => {
      return subject.get("parent") === (this.parentSubject!== null ? this.parentSubject.get('id'): null)
    });

    this.proxyCollection.reset(childSubjectsOfParentSubject);
    this.proxyCollection.trigger('change');
  },

  renderNestedSubjects: function (subject) {
    this.parentSubject = subject;
    let childSubjects = this.collection.filter((subject) => {
      return subject.get("parent") === this.parentSubject.get('id');
    });
    this.proxyCollection.reset(childSubjects);
    this.proxyCollection.trigger('change');
  },

  renderTable: function () {
    let tableView = new TableView({
      el: this.$el.find('.subject-table'),
      collection: this.proxyCollection,
      parentSubject: this.parentSubject,
      exam_template_type: this.exam_template_type,
    }).render();
    this.subviews['tableView'] = tableView;
    this.listenTo(tableView, 'subject-clicked', this.renderNestedSubjects);
  },

  renderChart: function() {
    this.subviews['chartView'] = new BarChartView({
      el: this.$el.find('.subjects-bar-chart'),
      chartClass: BarChartClass,
      collection: this.proxyCollection,
    }).render();
  },

  render: function () {
    this.$el.html(this.template({parentSubject: this.parentSubject}));
    this.renderTable();
    this.renderChart();
  }
});

export {ContainerView}
