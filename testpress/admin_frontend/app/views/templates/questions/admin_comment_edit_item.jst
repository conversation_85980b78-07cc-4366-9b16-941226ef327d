<% if (direction) { %>
<div class="item">
  <p class="add-question-title">Direction</p>
  <textarea id="direction" class="edit-mod-question"><%= direction.html %></textarea>
</div>
<% } %>

<div class="item">
  <p class="add-question-title">Question</p>
  <textarea id="question-html" class="edit-mod-question"><%= question_html %></textarea>
</div>
<p class="add-question-title">Options</p>
<% _.each(answers, function(answer, index){ %>
<div class="item option">
  <input type="hidden" name="id" value="<%= answer.id %>"/>
  <textarea id="text-html-<%= answer.id %>" class="edit-mod-option"><%= answer.text_html %></textarea><div class="mod-option-actions">
  <span>Is correct?</span><input name="is_correct" type="checkbox" <% if (answer.is_correct) { %>checked="checked"<% } %>/></div>
</div>
<%})%> <!-- end for loop -->
<div class="item">
  <p class="add-question-title">Explanation</p>
  <textarea id="explanation-html" class="edit-mod-question"><%= explanation_html %></textarea>
</div>
<div class="admin-edit-actions">
  <button class="btn btn-small btn-qs-create save">Save</button>
  <button class="btn btn-small cancel">Cancel</button>
</div>
