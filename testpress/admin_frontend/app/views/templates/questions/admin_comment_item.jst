<legend>Question
  <a class="btn btn-mini btn-qs-create admin-edit-content" href="#"> <i class="icon-pencil"></i>Edit</a>

  <div id="admin_view_thread_<%= id %>" class="pull-right btn-group dropleft">
      <a data-target="#admin_view_thread_<%= id %>" data-toggle="dropdown"
         class="btn btn-mini btn-qs-create dropdown-toggle" data-original-title="" title=""
         style="margin-top:10px;margin-right:5px;"> <i class="icon-zoom-in"> </i> View related Exams</a>
      <ul class="dropdown-menu" id="comments-exams-list">
        <!-- dropdown menu links -->
      </ul>
  </div>
</legend>
<% if ( direction) { %>
<div class="mod-direction">
  <%= direction.html_with_custom_font %>
</div>
<% } %>
<div class="mod-question">
  <div class="contents"><%= question_html_with_custom_font %></div>
</div>
<% _.each(answers, function(answer, index){ %>
<div class="mod-option">
  <div class="contents">
  <span 
    <% if (answer.is_correct) { %>
        class="btn-success option-circle-normal"
    <% } else { %>
        class="option-circle-normal"
    <% } %>><%= String.fromCharCode(97 + index) %></span>
 <span class="review-exam-options"><%= answer.text_html_with_custom_font %></span>
 </div>
</div>
<%})%> <!-- end for loop -->
<% if ( explanation_html ) { %>
<div class="mod-direction">
  <p class="add-question-title">Explanation</p>
  <%= explanation_html_with_custom_font  %>
</div>
<% } %>

