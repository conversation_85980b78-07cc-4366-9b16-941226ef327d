import View from 'views/view';


var CategoryItemView =  View.extend(
{
  tagName: "li",

  events: {
    'click': 'changeCategory',
  },

  initialize: function(options) {
    this.active = options.active;
  },

  changeCategory: function(evt) {
    this.collection.trigger('change-category', this.model);
  },

  render: function() {
    var data = this.model;
    this.$el.html(data);
    if (this.active) this.$el.addClass('active')
    return this;
  },
});

export default View.extend(
{
  tagName: 'ul',

  className: 'unstyled',

  initialize: function(options) {
    this.active_img = options.active_img;
    this.listenTo(this.collection, 'change-category', this.changeCategory);
    this.listenTo(this.collection, 'image-selected', this.imageSelected);
    this.active_model = this.collection.find(function(model) {
      return model.get('icon').indexOf(options.active_img) > -1; });
    if (this.active_model === undefined) this.active_model = this.collection.at(0);

  },

  imageSelected: function(src) {
    this.active_img = src;
    this.active_model = this.collection.find(function(model) { return model.get('icon').indexOf(src) > -1; });
    if (this.active_model === undefined) this.active_model = this.collection.at(0);
  },

  changeCategory: function(category) {
    this.active_model = this.collection.find(function(model) {
       return model.get('category') == category
    });
    this.render();
  },

  render: function() {

    this.$el.html('');
    var self = this;

    if (!this.collection.isEmpty()) {
      var categories = _.uniq(this.collection.pluck('category'));
      for (var category of categories) {
        self.renderItem(category, self.active_model.get('category') === category ? true : false)
      }
    }
    if (this.active_model.get('category').toLowerCase() === 'custom') {
      _.defer(() => {$('.custom').removeClass('hidden')});
    } else {
      $('.custom').addClass('hidden');
    }
    return this;
  },

  renderItem: function(item, active) {
    var itemView = new CategoryItemView({
      model: item,
      active: active,
      collection: this.collection
    });
    this.$el.append(itemView.render().el);
    this.subviews[item] = itemView;
  },

});
