import View from 'views/view';


var ImageItemView = View.extend(
{
  className: 'image-item',

  events: {
    'click': 'selectImage',
  },

  initialize: function(options) {
    this.src = options.src;
    this.active = options.active;
    this.category = options.category;
  },

  selectImage: function (evt) {
    this.collection.trigger('image-selected', this.src);
  },


  render: function () {
    this.$el.html('<img class="img-circle" src="' + this.src + '"/>');
    if (this.active) this.$el.addClass('active')
    return this;
  }
});

export default View.extend(
{
  initialize: function(options) {
    this.active_img = options.active_img;
    this.listenTo(this.collection, 'change-category', this.changeCategory);
    this.listenTo(this.collection, 'image-selected', this.imageSelected);
    this.listenTo(this.collection, 'add', this.addItem);
    this.listenTo(this.collection, 'custom-icons-fetched', this.createScrollObserver);
    this.active_model = this.collection.find(function(model) { return model.get('icon').indexOf(options.active_img) > -1; });
    if (this.active_model === undefined) this.active_model = this.collection.at(0);
  },

  imageSelected: function(src) {
    this.active_img = src;
    this.active_model = this.collection.find(function(model) { return model.get('icon').indexOf(src) > -1; });
    if (this.active_model === undefined) this.active_model = this.collection.at(0);
    this.render();
  },

  changeCategory: function(category) {
    this.active_model = this.collection.find(function(model) {
       return model.get('category') == category;
    });
    this.render();
  },

  render: function() {
    this.$el.html('');
    var self = this;
    _.each(this.collection.where({category: this.active_model.get('category')}), function(src, index, list) {
      self.renderItem(src.get('icon'), self.active_img === src.get('icon') ? true : false, src.get('category'));
    });
    this.delegateEvents();
    if (this.active_model.get("category").toLowerCase() == "custom") {
      setTimeout(() => {this.createScrollObserver()}, 1000);
    };
    return this;
  },

  addItem: function(model) {
    this.renderItem(model.get('icon'), false, model.get('category'));
  },

  renderItem: function(src, active, category) {
    if (category === 'Custom' && !src) {
      var message = `<div class="custom_icon empty">
        <div class="title">No icons available.</div>
        <div>To add an icon, click the upload button.</a></div>
      </div>`;
      this.$el.html(message);
      return;
    }

    var itemView = new ImageItemView({
      src: src,
      active: active,
      collection: this.collection,
      category: category
    });
    this.$el.append(itemView.render().el);
    this.subviews[src] = itemView;
  },

  createScrollObserver: function() {
    let target = $(".image-item").last()[0];
    if (!target) return;

    let options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1
    };
    let observer = new IntersectionObserver(_.bind(this.handleIntersect, this), options);
    observer.observe(target)
  },

  handleIntersect: function(entries, observer) {
    if (this.active_model.get("category").toLowerCase() == "custom") {
      if (entries[0].isIntersecting) { 
        this.collection.trigger('fetch-custom-icons');
        observer.disconnect();
      }
    }
  },
});
