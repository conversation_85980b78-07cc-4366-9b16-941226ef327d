import View from 'views/view';
import {CustomIconCollection, CustomIconModel} from 'models/custom_icons';
import UppyView from 'views/uppy-widget';
import image_groups from 'views/utils/image_group';
import MainTemplate from '../templates/common/imagepicker';
import CategoryListView from './category';
import ImageGridView from './icon';
import {createAnchorTag} from 'views/utils/functions';
import config from 'config';


var ChromeView = View.extend(
{
  template: MainTemplate,

  events: {
    'click .uppyOpen': 'openUppy'
  },

  initialize: function(options) {
    this.active_img = options.active_img;
    this.initCategories();
    this.initImageGrid();
  },

  openUppy: function() {
    this.collection.trigger('toggle-uppy');
  },

  initCategories: function() {
    this.categories = new CategoryListView({
      collection: this.collection,
      active_img: this.active_img
    });
    this.subviews.categories = this.categories;
  },

  initImageGrid: function() {
    this.grid = new ImageGridView({
      collection: this.collection,
      active_img: this.active_img
    });
    this.subviews.grid = this.grid;
  },

  render: function() {
    this.$el.html(this.template());
    this.categories.setElement(this.$el.find('.categories')).render();
    this.grid.setElement(this.$el.find('.image-grid')).render();
    return this;
  },
});

export default View.extend(
{
  events: {
    'click': 'showPopover',
  },

  initialize: function (options) {
    this.collection = new CustomIconCollection(image_groups);
    this.listenTo(this.collection.fullCollection, 'image-selected', this.imageSelected);
    this.listenTo(this.collection.fullCollection, 'toggle-uppy', this.toggleUppy);
    this.listenTo(this.collection.fullCollection, 'fetch-custom-icons', this.fetchCustomIcons);
    this.listenTo(this.collection, 'request', this.request);
    this.listenTo(this.collection, 'sync', this.sync);

    this.active_img = createAnchorTag(this.$el.find('img').attr('src'));
    var self = this;

    this.collection.fetch();
    this.chrome = new ChromeView({
      collection: this.collection.fullCollection,
      active_img: this.active_img
    });
    this.subviews.chrome = this.chrome;

    $(document).on('click', 'body', function (evt) {
      self.hidePopover(evt);
    });
    this.uppyView = new UppyView({
      el: '.uppy',
      target: '#uppy',
      restrictions: {allowedFileTypes: ['image/*'], maxNumberOfFiles:1},
      folder_name: "custom_icons",
      success_callback: this.success_callback.bind(this),
      is_public: true
    });
    $('.uppy-Dashboard-overlay').on('click', () => {
      this.uppyView.close();
    });
    $('.uppy-Dashboard-close').on('click', () => {
      this.uppyView.close();
    });
  },

  sync: function() {
    $('.loading').addClass('hidden');
  },

  request: function() {
    $('.loading').removeClass('hidden');
  },

  success_callback: function(fileId, data) {
    var url = data.location;
    url = url.substring(url.indexOf("institute/"));
    this.imageSelected(config.cdn_url + url);
    var custom_icon_model = new CustomIconModel({
      'icon':url, 'category': 'Custom'
    });
    this.uppyView.close();
    custom_icon_model.save(null, {
      success: (model, xhr, options) => {
        this.collection.fetch({add:true});
      }
    });
  },

  fetchCustomIcons: function() {
    if (this.collection.hasNextPage() && this.collection.next) {
      this.collection.getNextPage({
        fetch: true
      }).done(() => {
        this.collection.fullCollection.trigger("custom-icons-fetched");
      });
    }
  },

  setDjangoHiddenFormValue: function(src) {
    let url = new URL(src)
    let slash_length = 1
    let host_name_length = url.origin.length + slash_length
    let relative_url = url.toString().substring(host_name_length)
    let relative_institute_file_path = relative_url.substring(relative_url.indexOf("institute"));
    this.$el.find('input').val(relative_institute_file_path);
  },

  imageSelected: function(src) {
    this.active_img = src;
    this.$el.find('img').attr('src', src);
    this.setDjangoHiddenFormValue(src);
  },

  toggleUppy: function() {
    this.uppyView.open();
  },

  showPopover: function(evt) {
    var self = this;
    this.$el.popover({
      trigger: 'manual',
      template: '<div class="popover imagepicker"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>',
      html: true,
      content: this.chrome.render().$el
    }).on('shown', function() {
      self.redelegate();
    });
    this.$el.popover('show');
    _(function() {
    }).defer();
  },

  hidePopover: function(evt) {
    if (!this.$el.is(evt.target) && this.$el.has(evt.target).length === 0 && $('.popover').has(evt.target).length === 0) {
      this.$el.popover('destroy');
    }
    $('body').unbind('click');
  }
});

