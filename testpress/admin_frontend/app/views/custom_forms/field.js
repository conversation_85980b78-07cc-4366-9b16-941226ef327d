import App from 'application';

//Views
import View from 'views/view';
import WidgetView from 'views/custom_forms/widget.js';
import EditableWidgetView from 'views/custom_forms/editable_widget.js';

//Models
import {CustomField} from 'models/custom_forms';

//Templates
import MainTemplate from 'templates/custom_forms/field';


export default View.extend(
{
  className: 'field',

  template: MainTemplate,

  events: {
    'click .edit': 'editField',
    'click .add': 'addField',
    'click .duplicate': 'duplicateField',
    'click .remove': 'removeField',
    'click .widget': 'editField',
    //Prevent bubbling up if click happening inside field
    //Else parent would trigger blur-editable
    'click': (evt) => { evt.stopPropagation(); }
  },

  initialize: function(options) {
    this.custom_form = options.custom_form;
    this.initWidget();
    this.initEditableWidget();
    this.listenTo(this.model, 'remove', this.remove);
  },

  initWidget: function() {
    this.widget = new WidgetView({
      model: this.model
    });
    this.subviews.widget = this.widget;
  },

  initEditableWidget: function() {
    this.editable_widget = new EditableWidgetView({
      model: this.model,
      custom_form: this.custom_form
    });
    this.subviews.editable_widget = this.editable_widget;
  },

  render: function() {
    this.$el.html(this.template());
    this.$el.find('.widget-content').append(this.widget.render().el);
    this.$el.find('.widget-content').append(this.editable_widget.render().el);
    return this;
  },

  editField: function(evt) {
    if (evt != undefined) {
      evt.stopPropagation();
      evt.preventDefault();
    }
    if (this.$el.hasClass('focussed')) {
      return;
    }
    App.bus.trigger('blur-editable');
    this.widget.hide();
    this.editable_widget.show();
    this.listenTo(App.bus, 'blur-editable', this.blurEditable);
    this.$el.addClass('focussed');
  },

  addField: function(evt, field) {
    if (evt) evt.preventDefault();

    if (field === undefined) {
      field = new CustomField({
        label: "Untitled Question",
        field_type: 7,
        field_choices: ['First Choice', 'Second Choice', 'Third Choice'],
        custom_form: this.custom_form.get('id'),
        order: this.model.get('order') + 1
      });
    }

    let options = {
      success: _.bind(this.refreshCollection, this),
      error: (function(model, xhr, options) {
        model.set('error', true);
      }).bind(this)
    }
    field.save([], options);
  },

  duplicateField: function(evt) {
    if (evt) evt.preventDefault();

    let field = this.model.clone();
    field.unset('id');
    field.set('order', this.model.get('order') + 1);
    this.addField(null, field);
  },

  refreshCollection: function() {
    this.collection.fetchAll({
      custom_form: this.custom_form.get('id')
    });
  },

  removeField: function(evt) {
    if (evt) evt.preventDefault();

    let options = {
      success: _.bind(this.refreshCollection, this),
      error: (function(model, xhr, options) {
        model.set('error', true);
      }).bind(this)
    }
    this.model.destroy(options)
  },

  blurEditable: function() {
    this.stopListening(App.bus, 'blur-editable');
    this.editable_widget.hide();
    this.widget.show();
    this.$el.removeClass('focussed');
  }
});
