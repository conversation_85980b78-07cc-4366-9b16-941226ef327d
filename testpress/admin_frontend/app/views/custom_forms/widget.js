import View from 'views/view';
import WidgetTemplate from 'templates/custom_forms/widget';


export default View.extend(
{
  template: WidgetTemplate,

  className: 'span8 widget',

  initialize: function(options) {
    this.listenTo(this.model, 'change', this.render);
  },

  render: function() {
    let context = this.model.toJSON();
    context['cid'] = this.model.cid;
    this.$el.html(this.template(context));
    return this;
  }

});

