import App from 'application';
import View from 'views/view';
import Template from 'templates/custom_forms/editable_widget';
import ChoiceListView from 'views/custom_forms/choices_list_widget';
import Select2View from 'views/select2-widget'
import {NUMBER_TYPE, DECIMAL_TYPE, SHORT_TEXT_TYPE, PARAGRAPH_TYPE, EMAIL_TYPE, WEBSITE_TYPE} from 'views/custom_forms/constants'
import {addWysiwyg} from 'views/utils/functions';
const DECISION_BOX = 15;

export default View.extend(
{
  template: Template,

  className: 'editable-widget clearfix hidden',

  events: {
    'change #field_type_select': 'fieldTypeChanged',
    'select2:select #display_location_select': 'save',
    'select2:unselect #display_location_select': 'save',
    'select2:clear #display_location_select': 'save',
    'click .close-alert': 'resetErrorMessage',
    'blur [name="label"]': 'save',
    'click .is_required': 'changeIsRequired',
    'click .is_hidden': 'changeIsHidden',
    'change [name="is_unique"]': 'save',
    'keyup [name="label"]': 'saveOnEnter',
    'focus [name="label"]': 'selectAllText',
    'keyup': 'goToNormalWidget',
  },

  initialize: function(options) {
    this.custom_form = options.custom_form;
    this.unique_fields = [NUMBER_TYPE, DECIMAL_TYPE, SHORT_TEXT_TYPE, PARAGRAPH_TYPE, EMAIL_TYPE, WEBSITE_TYPE]
    this.hasFormErrors = false;
  },

  selectAllText: function(evt) {
    evt.target.select();
  },

  reRender: function() {
    // We are undelegating here else it will trigger blur event
    // as we are remove the element from the DOM
    this.undelegateEvents();
    this.render();
    this.delegateEvents();
  },

  goToNormalWidget: function(evt) {
    if (evt.key === "Escape") {
      App.bus.trigger('blur-editable');
    }
  },

  saveAndRender: function(evt) {
    this.save();
    this.reRender();
  },

  canShowUniqueOption: function(field_type) {
    return this.unique_fields.includes(parseInt(field_type))
  },

  fieldTypeChanged: function(evt) {
    this.saveAndRender();
    this.$el.find(".unique").addClass("hidden")
    if (this.canShowUniqueOption(evt.target.value)) {
      this.$el.find(".unique").removeClass("hidden")
    }
    this.$el.find('[name="label"]').focus();
  },

  show: function() {
    View.prototype.show.apply(this, arguments);
    this.$el.find('[name="label"]').focus();
    let instance = CKEDITOR.instances[this.cid];
    this.focusCKEditorInstance(instance);
    this.initDisplayLocationSelectView();
  },

  isEnterKey: function(evt) {
    if (evt.type === 'keyup' && evt.which !== 13) return false;

    return true;
  },

  saveOnEnter: function(evt) {
    if (!this.isEnterKey(evt)) return;
    this.save();
  },

  changeIsRequired: function(evt) {
    $(evt.target).prop('checked', !this.model.get("is_required"));
    this.toggleIsHiddenField(!evt.target.checked);
    this.save(evt);
  },

  toggleIsHiddenField: function(show) {
    this.$el.find('.is_hidden').prop('checked', false);
    if (show) {
      this.$el.find('.is_hidden_container').show();
      this.$el.find('.is_hidden').prop('disabled', false);
    }
    else {
      this.$el.find('.is_hidden').prop('disabled', true);
      this.$el.find('.is_hidden_container').hide();
    }
  },

  changeIsHidden: function(evt) {
    $(evt.target).prop('checked', !this.model.get("is_hidden"));
    this.toggleIsRequiredField(!evt.target.checked);
    this.toggleDisplayLocationField(!evt.target.checked);
    this.save(evt);
  },

  toggleIsRequiredField: function(show) {
    this.$el.find('.is_required').prop('checked', false);
    if (show) {
      this.$el.find('.is_required_container').show();
      this.$el.find('.is_required').prop('disabled', false);
    }
    else {
      this.$el.find('.is_required').prop('disabled', true);
      this.$el.find('.is_required_container').hide();
    }
  },

  toggleDisplayLocationField: function(show) {
    this.$el.find('#display_location_select').val('').trigger('change');
    if (show) {
      this.$el.find('#display_location_container').show();
      this.$el.find('#display_location_select').prop('disabled', false);
    }
    else {
      this.$el.find('#display_location_select').prop('disabled', true);
      this.$el.find('#display_location_container').hide();
    }
  },

  save: function(evt) {
    let label = null;
    if(this.model.get("field_type") === DECISION_BOX){
      label = this.getLabel();
    } else {
      label = this.$el.find('[name="label"]').val();
    }
    let field_type = parseInt(this.$el.find('option:selected').val(), 10);
    this.model.set({
      'label': label,
      'field_type': field_type,
      'custom_form': this.custom_form.get('id'),
      'is_required': this.$el.find('.is_required').is(":checked"),
      'is_hidden': this.$el.find('.is_hidden').is(":checked"),
      'is_unique': this.$el.find('[name="is_unique"]').is(":checked"),
      "display_location": this.$el.find("#display_location_select").val(),
    });

    if(!_.contains([7, 8, 9, 10], field_type)) {
        this.model.set({
          'field_choices': []
        });
    }

    if (this.custom_form.isNew()) {
      let options = {
        'success': () => {
          this.model.set({
            'custom_form': this.custom_form.get('id')
          })
          this.model.save();
        }
      };
      if (this.custom_form.get("name") === undefined) {
        this.custom_form.set('name', 'Untitled Form');
      }
      this.custom_form.save([], options)
    } else {
      this.model.save();
    }
  },

  getLabel: function() {
    let instance = CKEDITOR.instances[this.cid];
    let label = instance.getData();
    instance.on('change', () => {
      label = instance.getData();
    });
    instance.on('blur', () => {
      this.save();
    });
    return label;
  },

  setSelectedFieldType: function() {
    this.$el.find("select[name='field_type'] option").each((index, element) => {
      if ($(element).val() == this.model.get('field_type')) {
        $(element).prop('selected', 'selected');
      }
    });
  },

  isChoiceField: function() {
    let field_type = parseInt(this.model.get('field_type'), 10);
    return _.contains([7, 8, 9, 10], field_type);
  },

  initDisplayLocationSelectView: function(evt) {
    let display_location_view = new Select2View({
      el: this.$el.find("#display_location_select"),
      multiple: true,
      templateSelection: function(option){return option.text},
    });
    this.subviews.display_location_view = display_location_view
    this.model.get('is_hidden') ? this.toggleDisplayLocationField(false) : null;
  },

  render: function() {
    let context = this.model.toJSON();
    context['form_type'] = this.custom_form.get('form_type');
    context['formElementId'] = this.cid;
    this.$el.html(this.template(context));
    if(this.isChoiceField()) {
      this.subviews['choices'] = new ChoiceListView({
        model: this.model,
        custom_form: this.custom_form
      });
      this.subviews['choices'].setElement(this.$el.find('.choices')).render();
    }
    if(this.canShowUniqueOption(this.model.get("field_type"))) {
      this.$el.find(".unique").removeClass("hidden")
    }
    this.$el.find('[data-toggle="tooltip"]').tooltip();
    if(this.model.get("field_type") === DECISION_BOX){
      _.defer(() => {
        this.configureWysiwygEditor();
      });
    }
    this.setSelectedFieldType();
    this.initDisplayLocationSelectView();
    return this;
  },

  configureWysiwygEditor: function() {
    let removetoolbarGroup = ['insert'];
    addWysiwyg(this.cid, removetoolbarGroup);
    let instance = CKEDITOR.instances[this.cid];
    let label = this.model.get('label');
    this.initializeCKEditorInstance(label, instance);
    instance.on('instanceReady', () => {
      this.focusCKEditorInstance(instance);
    });
    instance.on('blur', () => {
      this.save();
    });
  },

  initializeCKEditorInstance: function(label, instance) {
    if(label === "Untitled Question"){
      instance.setData('Enter a Text...');
    } else if(label) {
      instance.setData(label);
    }
  },

  focusCKEditorInstance: function(instance) {
    if (!instance) return;
    instance.focus();
    instance.execCommand('selectAll');
  },
});
