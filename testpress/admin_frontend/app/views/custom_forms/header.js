import View from 'views/view';


export default View.extend(
{
  events: {
    'keyup': 'validateTitle',
    'change': 'saveModel'
  },

  tagName: 'textarea',

  attributes: {
    'rows': '1',
    'placeholder': 'Type a title...'
  },

  initialize: function(options) {
    this.listenTo(this.model, 'change', this.render);
  },

  render: function() {
    this.$el.html(this.model.get('name'));
    _.defer(() => {
      this.$el.autosize();
      this.$el.trigger('autosize:update');
    });
    return this;
  },

  saveModel: function(evt) {
    let form_title = this.$el.val();
    if (form_title.length !== 0 ) {
      this.model.set('name', form_title);
      this.model.save()
    }
  },

  validateTitle: function (evt) {
    let $heading = $(".heading");
    let form_title = this.$el.val();
    if (form_title.length !== 0 ) {
      $heading.css("border-bottom-color", "#ddd");
      $heading.tooltip('destroy');
      return true;
    } else {
      $heading.attr({
        "title": "Title can't be empty.",
        "data-toggle": "tooltip"
      });
      $heading.css("border-bottom-color", "red");
      $heading.tooltip();
      $heading.tooltip('show');
      return false;
    }
  }
});


