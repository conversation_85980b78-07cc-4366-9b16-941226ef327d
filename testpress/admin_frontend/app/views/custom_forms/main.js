import App from 'application';
import View from 'views/view';

//Subviews
import <PERSON>er<PERSON>ie<PERSON> from 'views/custom_forms/header';
import FormView from 'views/custom_forms/form';

//Templates
import MainTemplate from 'templates/custom_forms/main';

const PUBLISHED = 2;

export default View.extend(
{
  template: MainTemplate,

  events:{
    'click #publish': 'publish',
    'click #draft': 'saveDraft',
  },

  initialize: function(options) {
    this.course = options.course;
    this.form_type = options.form_type;
    this.model.set('course', this.course);
    this.model.set('form_type', this.form_type);
    this.message = options.message;
    this.redirect_url = options.redirect_url;
    this.initHeader();
    this.initForm();
    $(document).on('click.blur-editable', function(event) {
      App.bus.trigger('blur-editable');
    });
    this.listenTo(this.model, 'change:status', this.togglePublishButton);
  },

  initHeader: function() {
    this.header = new HeaderView({
      model: this.model
    });
    this.subviews.header = this.header;
  },

  initForm: function() {
    this.form = new FormView({
      collection: this.collection,
      custom_form: this.model
    });
    this.subviews.form = this.form;
  },

  togglePublishButton: function () {
    if (this.model.get("status") === PUBLISHED) {
      this.$el.find('#publish').html('Re-Publish');
    } else {
      this.$el.find('#publish').html('Publish');
    }
  },

  render: function() {
    this.$el.html(this.template());
    this.$el.find('.heading').append(this.header.render().el);
    this.$el.find('.form').append(this.form.render().el);
    this.togglePublishButton();
    return this;
  },

  remove: function() {
    $(document).off('click.blur-editable');
  },

  saveCollection: function() {
    _.each(this.collection.models, function (model) {
      model.save();
    });
  },

  saveDraft: function() {
    if (this.header.validateTitle()) {
      this.model.set('status', 1);
      this.$el.find('#draft').addClass('disabled').attr('disabled', 'disabled').html('Saving...')
      let options = {
        success: (model, xhr, options) => {
          this.$el.find('#draft').removeClass('disabled').removeAttr("disabled").html('Save as draft');
        },
        error: (function (model, xhr, options) {
          this.$el.find('#draft').removeClass('disabled').removeAttr("disabled").html('Save as draft');
        }).bind(this)
      }
      this.model.save([], options);
    }
  },

  publish: function () {
    if (this.header.validateTitle()) {
      this.model.set('status', 2);
      this.$el.find('#publish').addClass('disabled').attr('disabled', 'disabled').html('Publishing...');
      let options = {
        success: (model, xhr, options) => {
          this.$el.find('#publish').removeClass('disabled').removeAttr("disabled").html('Re-Publish');
          if (this.redirect_url !== undefined) {
            window.location.replace(this.redirect_url);
          } else if (this.message !== undefined) {
            this.$el.find('#message').text(this.message);
            this.$el.find('#message').show();
            this.$el.find('#message').fadeOut(3000);
          }
        },
        error: (function (model, xhr, options) {
          this.$el.find('#publish').removeClass('disabled').removeAttr("disabled").html('Publish');
        }).bind(this)
      };
      this.model.save([], options);
    }
  }
});


