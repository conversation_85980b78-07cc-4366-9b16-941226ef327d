import App from 'application';
import View from 'views/view';
import ChoiceTemplate from 'templates/custom_forms/choice';
import ChoicesListTemplate from 'templates/custom_forms/choices_list';

function isEnterKey(evt) {
  if (evt.type === 'keyup' && evt.which !== 13) return false;
  return true;
}

var ChoiceView = View.extend(
{
  template: ChoiceTemplate,

  emptyTemplate: _.template('<input type="text" name="choice" value="" placeholder="Add a choice" class="input-xlarge">'),

  events: {
    'click .delete': 'deleteChoice',
    'keyup input': 'save',
    'blur input': 'save',
    'focus input': 'selectAllText',
  },

  initialize: function(options) {
    this.index = options.index;
    this.value = options.value || '';
    this.previousView = options.previousView;
    this.listenTo(this.model, 'change:field_choices', this.checkFieldChoice);
  },

  checkFieldChoice: function() {
    if (this.model.get("field_choices").length > 1) {
      this.$el.find('.delete').show();
    } else {
      this.$el.find('.delete').hide();
    }
  },

  deleteChoice: function() {
    let field_choices = _.clone(this.model.get('field_choices'));
    field_choices.splice(this.index, 1);
    this.model.set({
      'field_choices': field_choices
    });
    this.model.save();
    this.model.trigger('refresh-index');
    this.remove();
  },

  refreshIndex: function() {
    if (this.value) {
      this.index = _.indexOf(this.model.get('field_choices'), this.value);
    }
  },

  //previousView would be set for add new choice input only
  setPreviousView: function(view) {
    this.previousView = view;
  },

  selectAllText: function(evt) {
    this.$el.find('input').select();
  },

  render: function() {
    if (this.index !== undefined) {
      this.$el.html(this.template({
        'value': this.value
      }));
    } else {
      this.$el.html(this.emptyTemplate());
    }
    _.defer(() => {
      this.listenTo(this.model, 'refresh-index', this.refreshIndex);
    });
    this.checkFieldChoice();
    return this;
  },

  save: function(evt) {
    //let field_choices = this.getFieldChoices();
    let value = this.$el.find('input').val().trim();
    let duplicate = false;
    let index = _.indexOf(this.model.get('field_choices'), value);
    if (index > -1 && index !== this.index) {
      duplicate = true;
    }

    if (duplicate) {
      this.$el.find('input').addClass('error');
    } else {
      this.$el.find('input').removeClass('error');
    }

    if (!isEnterKey(evt)) return;

    if (duplicate || !value) {
      this.$el.find('input').val(this.value);
      this.$el.find('input').removeClass('error');
    } else {
      let field_choices = _.clone(this.model.get('field_choices'));

      if (this.index === undefined) {
          //Add new choice field case

          if (this.previousView) {
            field_choices.splice(this.previousView.index + 1, 0, value);
            this.trigger('add-choice', this.previousView.index + 1, value, this.cid);
          } else {
            this.trigger('add-choice', field_choices.length, value, this.cid);
            field_choices.push(value);
          }
          this.$el.find('input').val('');
      } else {
          field_choices[this.index] = value;
          this.value = value;
          this.$el.find('input').val(value);
      }

      this.model.set({
        'field_choices': field_choices
      });
      this.model.save();
      this.model.trigger('refresh-index');
    }

    if (evt.type === 'keyup' && this.index !== undefined) {
      this.trigger('add-new', this.cid);
    }

    if (evt.type === 'blur' || evt.type === 'focusout') {
      if (this.previousView) this.remove();
    }
  },

});


export default View.extend(
{
  template: ChoicesListTemplate,

  renderChoice: function(index, value, view_id) {
    let choiceView = new ChoiceView({
      index: index,
      value: value,
      model: this.model
    });
    this.subviews[choiceView.cid] = choiceView;
    this.listenTo(choiceView, 'add-new', this.renderAddNewChoice);
    if (view_id) {
      choiceView.render().$el.insertBefore(this.subviews[view_id].el);

      if (this.subviews[view_id].previousView) {
        //This is triggered by add-choice by a new view
        // So set the new child view as it's previousView
        // The last new choice view won't have a previousView.
        // Leave it as it is.
        this.subviews[view_id].setPreviousView(choiceView);
      }
    } else {
      this.$el.find('.list').append(choiceView.render().el);
    }
  },

  renderAddNewChoice: function(view_id) {
    let addNewChoiceView = new ChoiceView({
      model: this.model
    });
    this.subviews[addNewChoiceView.cid] = addNewChoiceView;
    this.listenTo(addNewChoiceView, 'add-choice', this.renderChoice);
    if (this.subviews.hasOwnProperty(view_id)) {
      addNewChoiceView.setPreviousView(this.subviews[view_id]);
      addNewChoiceView.render().$el.insertAfter(this.subviews[view_id].el);
      addNewChoiceView.selectAllText();
    } else {
      this.$el.find('.list').append(addNewChoiceView.render().el);
    }
  },

  render: function() {
    this.$el.html(this.template());
    _.each(this.model.get('field_choices'), (value, index) => {
      this.renderChoice(index, value);
    });
    this.renderAddNewChoice();
    return this;
  }

});

