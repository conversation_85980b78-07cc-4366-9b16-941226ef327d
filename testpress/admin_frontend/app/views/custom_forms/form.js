import View from 'views/view';

//Subviews
import FieldView from 'views/custom_forms/field';

//Models
import {CustomField} from 'models/custom_forms';

//Templates
import EmptyFormTemplate from 'templates/custom_forms/empty';
import MainTemplate from 'templates/custom_forms/form';


export default View.extend(
{
  tagName: 'form',

  events: {
    'click .add-field': 'addNewField'
  },

  initialize: function(options) {
    this.custom_form = options.custom_form;
    this.listenTo(this.collection, 'add', this.addField);
    this.listenTo(this.collection, 'fetch-complete', this.renderHelpIfEmpty);
  },

  addNewField: function(evt) {
    if (evt) evt.preventDefault();

    //If collection is empty, EmptyFormTemplate must've been
    //rendered. Clear it
    if (this.collection.isEmpty()) this.$el.html(MainTemplate());

    let field = new CustomField({
      label: "Untitled Question",
      field_type: 7,
      field_choices: ['First Choice', 'Second Choice', 'Third Choice'],
      custom_form: this.custom_form.get('id'),
      order: 0
    });

    let field_save_options = {
      success: (model, xhr, options) => {
        this.collection.fetchAll({
          custom_form: this.custom_form.get('id')
        });
      },
      error: (function(model, xhr, options) {
        model.set('error', true);
      }).bind(this)
    }

    if (this.custom_form.isNew()) {
      // To prevent from creating multiple post requests for new custom form.
      this.$el.find('.add-field').hide();

      let form_save_options = {
        'success': () => {
          this.$el.find('.add-field').show();
          field.set({
            'custom_form': this.custom_form.get('id')
          })
          field.save([], field_save_options);
        }
      }
      if (this.custom_form.get("name") === undefined) {
        this.custom_form.set('name', 'Untitled Form');
      }
      this.custom_form.save([], form_save_options)
    } else {
      field.save([], field_save_options);
    }
  },

  renderHelpIfEmpty: function() {
    if (this.collection.isEmpty()) this.$el.html(EmptyFormTemplate());
  },

  render: function() {
    if (this.collection.isEmpty()) {
      this.$el.html(EmptyFormTemplate());
    } else {
      this.$el.html(MainTemplate());
      this.collection.each((item, index) => {
        this.renderItem(item);
      });
    }
    return this;
  },

  renderItem: function(model) {
    if (!this.subviews.hasOwnProperty(model.cid)) {
      var itemView = new FieldView({
        model: model,
        collection: this.collection,
        custom_form: this.custom_form
      });
      this.subviews[model.cid] = itemView;
    } else {
      var itemView = this.subviews[model.cid];
    }

    // Logic to decide whether to append at last or insert inbetween
    let previousModel = null;
    // Check if model order is > 0, otherwise it will return the model from the
    // back of the collection
    if (model.get('order') > 0) previousModel = this.collection.at(model.get('order') - 1);
    if (previousModel && 
        this.subviews.hasOwnProperty(previousModel.cid)) {
        itemView.render().$el.insertAfter(this.subviews[previousModel.cid].el);
    } else {
      itemView.render().$el.insertAfter(this.$el.find('.prepend-field'));
    }
  },

  addField: function(model) {
    this.renderItem(model)
    this.subviews[model.cid].editField();
  },

});


