import View from 'views/view';
import MainTemplate from 'templates/sections/question-bank-form';
import {SubjectModel} from 'models/subjects';
import {ExamQuestionCollection} from 'models/exam_questions';
import QuestionBankListView from 'views/sections/question-bank-list';

export default View.extend(
{
  template: MainTemplate,

  events: {
    'click #add-question': 'addSelectedQuestions'
  },

  subscribe: {
    'question-toggled': 'toggleAddButton',
  },

  initialize: function(options) {
    this.exam = options.exam;
    this.section = options.section;
    this.question_pool_id = options.question_pool_id;
    this.proxy_collection = new ExamQuestionCollection();
    this.initQuestionsList();
  },

  initQuestionsList: function() {
    this.questionBankList = new QuestionBankListView({exclude_exam: this.exam});
    this.subviews.questionBankList = this.questionBankList;
  },

  toggleAddButton: function() {
    if ($('input[name="questions"]:checked').length > 0) {
      $('#add-question').removeClass('hidden');
    } else {
      $('#add-question').addClass('hidden');
    }
  },

  addSelectedQuestions: function(evt) {
    evt.preventDefault();
    var urls = this.questionBankList.selectedQuestions.pluck('url');
    _.each(urls, (item, index) => {
      this.proxy_collection.add({
        'exam': this.exam.get('url'),
        'section': this.section.get('url'),
        'question': item,
        'adaptive_exam_question_pool': this.question_pool_id
      });
    });

    this.proxy_collection.save({
      success: (model, xhr, status) => {
        window.location.reload();
      }
    });
  },

  render: function() {
    this.$el.html(this.template());
    this.$el.append(this.questionBankList.render().el);
    this.questionBankList.setFilter();
    this.redelegate();
    return this;
  }
});
