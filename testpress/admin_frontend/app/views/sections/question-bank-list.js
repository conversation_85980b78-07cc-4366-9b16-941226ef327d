import View from 'views/view';
import Select2View from 'views/select2-widget';
import App from 'application';
import ItemTemplate from 'templates/sections/question-bank-item';
import HeaderTemplate from 'templates/sections/question-bank-header';
import HeaderPageInfoTemplate from 'templates/sections/question-bank-page-info';
import ItemEmptyTemplate from 'templates/sections/question-bank-item-empty';
import MainTemplate from 'templates/sections/question-bank-main'
import RelatedItemsTemplate from 'templates/sections/related-items';
import {QuestionCollection} from 'models/questions';
import {Paginator} from 'views/utils/paginator';
import {Exam} from 'models/exams';
import {SubjectModel} from 'models/subjects';

var ItemView = View.extend(
{
  template: ItemTemplate,

  tagName: 'li',

  events: {
    'change input[type="checkbox"]': 'questionToggled',
    'click .dropdown-toggle': 'loadRelatedItems'
  },

  initialize: function(options) {
    this.listenTo(this.model, 'remove', this.remove);
    this.listenTo(this.model, 'change', this.render);
  },

  loadRelatedItems: function(evt) {
    evt.preventDefault();
    if (!this.relatedItemsView) {
      this.relatedItemsView = new RelatedItemsView({
        model: this.model
      });
      this.relatedItemsView.setElement(
        this.$el.find('#related-' + this.model.id + ' .dropdown-menu')
      ).render();
    }
    this.relatedItemsView.loadItems();
  },

  questionToggled: function(evt) {
    _.defer(() => {
      App.bus.trigger('question-toggled',
              $(evt.target).is(':checked'), this.model);
    });
  },

  render: function() {
    var data = this.model.toJSON();
    if (data.direction) {
      data.short_direction = $.truncate(data.direction.html, {
        length: 200,
        words: true,
        noBreaks: true
      });
    }
    this.$el.html(this.template(data));
    this.delegateEvents();
    return this;
  }
});

var RelatedItemsView = View.extend({
  template: RelatedItemsTemplate,

  events: {
    'click .dropdown-toggle': 'loadItems'
  },

  initialize: function(options) {
    this.model = options.model;
    this.questionSet = null;
    this.exams = [];
  },

  render: function() {
    this.$el.html(this.template({
      questionSet: this.questionSet,
      exams: this.exams,
      loading: false
    }));
    return this;
  },

  loadItems: function() {
    this.$el.html(this.template({
      questionSet: null,
      exams: [],
      loading: true
    }));

    $.get('/api/v3/admin/questions/' + this.model.id + '/usages/')
      .done((data) => {
        this.questionSet = data.question_set;
        this.exams = data.exams;
        this.render();
      })
      .fail(() => {
        this.$el.html(this.template({
          questionSet: null,
          exams: [],
          loading: false,
          error: 'Failed to load related items.'
        }));
      });
  }
});


var ListView = View.extend(
{
  tagName: 'ul',

  className: 'unstyled questions-list',

  initialize: function(options) {
    this.listenTo(this.collection, 'reset', this.refresh);
    this.listenTo(this.collection, 'add', this.renderItem);
    this.listenTo(this.collection, 'request', this.startProgress);
    this.listenTo(this.collection, 'sync', this.stopProgress);
    this.listenTo(this.collection, 'expand-all', this.expandAll);
  },

  toggleCheckboxes: function(checked) {
    this.$el.find('input[name="questions"]').prop('checked', checked);

    let models = _.pluck(this.subviews, 'model');
    App.bus.trigger('question-toggled', checked, models);
  },

  expandAll: function() {
    this.$el.find('.short-passage').addClass('hidden');
    this.$el.find('.more-passage').addClass('hidden');
    this.$el.find('.long-passage').removeClass('hidden');
    this.$el.find('.short').addClass('hidden');
    this.$el.find('.long').removeClass('hidden');
  },

  startProgress: function() {
    // Clear the list view from the empty message
    if (this.collection.isEmpty()) {
      this.$el.html('');
    }
  },

  stopProgress: function() {
    if (this.collection.isEmpty()) {
      this.$el.html(ItemEmptyTemplate());
    }
  },

  refresh: function() {
    _.each(this.subviews, (view, index) => {
      view.remove();
    });
    this.render();
  },

  removeItem: function(view) {
    delete this.subviews[view.model.id];
  },

  renderItem: function(item) {
    if (!this.subviews.hasOwnProperty(item.id)) {
      var itemView = new ItemView({
        model: item
      });
      this.subviews[item.id] = itemView;
    } else {
      var itemView = this.subviews[item.id];
    }
    this.listenTo(itemView, 'remove', this.removeItem);
    this.$el.append(itemView.render().el);
    this.$el.highlight($('input[name="search"]').val());
    this.$el.find(".label").removeHighlight();
  },

  render: function() {
    this.$el.empty();
    this.collection.each(function(item, index) {
      this.renderItem(item);
    }, this);
    return this;
  }
});

var HeaderPageInfoView = View.extend(
{
  template: HeaderPageInfoTemplate,

  initialize: function(options) {
    this.listenTo(this.collection, 'sync', this.render);
  },

  getStartEnd: function() {
    let end = this.collection.state.pageSize * this.collection.state.currentPage;
    let start = end - this.collection.state.pageSize + 1;
    if (end > this.collection.state.totalRecords) {
      end = this.collection.state.totalRecords;
    }
    return {
      'start': start,
      'end': end
    }
  },

  render: function() {
    if (this.collection.length) {
      this.$el.removeClass('hidden');
      let startend = this.getStartEnd();
      this.$el.html(this.template({
        collection: this.collection,
        start: startend.start,
        end: startend.end
      }));
    } else {
      this.$el.addClass('hidden');
    }
    return this;
  }
});

var HeaderView = View.extend(
{
  template: HeaderTemplate,

  events: {
    'change .filter-item': 'filter',
    'change .select-all2': 'toggleCheckboxes',
    'click .clear-filter a': 'clearFilters',
    'click .expand-all a': 'expandAll',
    'keyup input[name="search"]': 'filter'
  },

  initialize: function(options) {
    this.exclude_exam = options.exclude_exam;
    this.listenTo(this.collection, 'sync', this.sync);
    this.page_info = new HeaderPageInfoView({
      collection: this.collection
    });
    this.subviews['page_info'] = this.page_info;
  },

  toggleCheckboxes: function(evt) {
    this.trigger('toggle-checkbox', $(evt.target).is(':checked'));
  },

  clearFilters: function(evt) {
    evt.preventDefault();
    this.$el.find('input[type=number],[type=date],[type=text]').val('');
    this.$el.find('select[name="subject"]').val(null).trigger('change.select2');
    this.$el.find('select[name="sort-questions"]').val('order').trigger('change.select2');
    this.$el.find('select[name="page_size"]').val('20').trigger('change.select2');
    this.$el.find('select[name="question_type"]').val('').trigger('change.select2');
    this.$el.find('select[name="question_set"]').val(null).trigger('change.select2');
    this.trigger('filter', evt);
  },

  expandAll: function(evt) {
    evt.preventDefault();
    this.collection.trigger('expand-all');
  },

  getFilterValues: function() {
    return {
      'o': this.$el.find(".sort-questions").val(),
      'percentage_got_correct_0': this.$el.find('[name="percentage_got_correct_0"]').val(),
      'percentage_got_correct_1': this.$el.find('[name="percentage_got_correct_1"]').val(),
      'difficulty_level': this.$el.find('[name="difficulty_level"]').val(),
      'page_size': this.$el.find('[name="page_size"]').val(),
      'subject': this.$el.find('[name="subject"]').val(),
      'include_child_subjects': 'on',
      'is_unused': this.$el.find('[name="filter_unused_questions"]').is(":checked"),
      'type': this.$el.find('[name="question_type"]').val(),
      'q': this.$el.find('[name="search"]').val(),
      'tags': this.$el.find('[name="tags"]').val(),
      'question_set': this.$el.find('[name="question_set"]').val(),
    }
  },

  filter: function(evt) {
    this.trigger('filter', evt);
  },

  sync: function() {
    if (this.collection.length) {
      this.$el.find('.select-all2').removeClass('hidden');
    } else {
      this.$el.find('.select-all2').addClass('hidden');
    }
    this.$el.removeClass('hidden');
  },

  subjectTemplate: function(subject) {
    return subject.tree_path;
  },

  render: function() {
    window.collection = this.collection;
    if (_.has(this, 'sort_select_view')) {
      this.sort_select_view.remove();
    }

    if (_.has(this, 'subjects_filter_view')) {
      this.subjects_filter_view.remove();
    }

    this.$el.html(this.template({
      collection: this.collection
    }));
    this.initFilters();
    this.sort_select_view = new Select2View({
      el: this.$el.find(".sort-questions"),
      hideSearch: true
    });
    this.subviews['sort_select_view'] = this.sort_select_view;

    this.subjects_filter_view = new Select2View({
      el: this.$el.find(".subjects-filter"),
      url: "/api/v2.3/admin/subjects/",
      templateSelection: this.subjectTemplate
    });
    this.subviews['subjects_filter_view'] = this.subjects_filter_view;


    this.page_size_view = new Select2View({
      el: this.$el.find(".page-size"),
      hideSearch: true
    });
    this.subviews['page_size_view'] = this.page_size_view;

    this.difficulty_level_view = new Select2View({
      el: this.$el.find(".difficulty-level"),
      hideSearch: true
    });
    this.subviews['difficulty_level_view'] = this.difficulty_level_view;

    this.page_info.setElement(this.$el.find('.page-info')).render();

    if (this.collection.length) {
      this.$el.find('.select-all2').removeClass('hidden');
    } else {
      this.$el.find('.select-all2').addClass('hidden');
    }

    if (this.exclude_exam) {
      //This will be removed on sync
      this.$el.addClass('hidden');
    }
    return this;
  },

  initFilters: function() {
    this.tags_filter_view = new Select2View({
      el: this.$el.find(".tags-filter"),
      url: '/api/v2.5/admin/tags/?has_questions=true',
      multiple: true,
      searchParam: "search",
      templateSelection: function (tag) {
        return tag.name || tag.text;
      },
    });
    this.subviews['tags_filter_view'] = this.tags_filter_view;

    this.initQuestionSetFilterView();
  },

  initQuestionSetFilterView: function() {
    this.question_set_filter_view = new Select2View({
      el: this.$el.find(".question-set-filter"),
      url: "/api/v2.5/admin/question_sets/",
      templateSelection: this.questionSetFilterTemplate,
      searchParam: "search",
    });
    this.subviews['question_set_filter_view'] = this.question_set_filter_view;
  },

  questionSetFilterTemplate: function(question_set) {
    return question_set.tree_path;
  },
});

export default View.extend(
{
  template: MainTemplate,

  subscribe: {
    'question-toggled': 'questionToggled',
  },

  className: 'questions-list-container',

  initialize: function(options) {
    this.exam_ids = [];
    this.exclude_exam = options.exclude_exam;
    this.collection = new QuestionCollection();
    this.selectedQuestions = new QuestionCollection();
    this.list = new ListView({
      collection: this.collection
    });
    this.subviews.list = this.list;
    this.header = new HeaderView({
      collection: this.collection,
      exclude_exam: this.exclude_exam
    });
    this.subviews.header = this.header;
    this.listenTo(this.header, 'filter', this.filter);
    this.listenTo(this.header, 'toggle-checkbox', this.toggleCheckboxes);
    this.paginator = new Paginator({
      collection: this.collection,
      renderMultiplePagesOnly: true
    });
    this.subviews.paginator = this.paginator;
  },

  filter: function() {
    this.selectedQuestions.reset();
    this.collection.reset();
    let filterValues = this.header.getFilterValues();
    let page_size = parseInt(filterValues['page_size'] || 10, 10);
    delete filterValues['page_size'];
    _.extend(this.collection.queryParams, filterValues);
    this.collection.setPageSize(page_size, {first: true, traditional: true});
  },

  toggleCheckboxes: function(checked) {
    this.list.toggleCheckboxes(checked);
  },

  questionToggled: function(checked, model) {
    if (checked) {
      this.selectedQuestions.add(model);
    } else {
      this.selectedQuestions.remove(model, {silent: true});
    }
  },

  setExamIds: function(data) {
    this.exam_ids = data
    this.setFilter()
  },

  setExcludeExamIds: function(exclude_ids) {
    this.exclude_exam = exclude_ids
    this.setFilter()
  },

  setFilter: function() {
    this.selectedQuestions.reset();
    _.extend(this.collection.queryParams, {
      'exam': this.exam_ids,
      'exclude_exam': Array.isArray(this.exclude_exam) ? this.exclude_exam : this.exclude_exam.get('id')
    });
    this.collection.reset();
    this.collection.getFirstPage({
      traditional: true
    });
  },

  render: function() {
    this.$el.html(this.template());
    this.header.setElement(this.$el.find('#header')).render();
    this.list.setElement(this.$el.find('#list')).render();
    this.paginator.setElement(this.$el.find('#paginator')).render();
    return this;
  }
});
