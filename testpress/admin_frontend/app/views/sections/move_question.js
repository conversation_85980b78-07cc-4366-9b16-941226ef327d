import View from 'views/view';
import MainTemplate from 'templates/sections/move_question';

export default View.extend(
{
  initialize: function(options) {
    this.close_button = '<button type="button" data-dismiss="popover" class="close close_popover" aria-hidden="true">&times;</button>';
    this.url = '/api/v2.4/admin/exams/questions/sort/'
  },

  events: {
    'click .close_popover': 'closePopover',
    'click .submit': 'changeOrder',
    'keyup .position': 'handleEnter',
    'click .move_content': 'openPopover'
  },

  openPopover: function (evt) {
    if ($(evt.target).next('div.popover:visible').length) {
      $(evt.target).popover('hide');
      return;
    }
    this.$el.find('.move_content').not($(evt.target)).popover('hide');
    $(evt.target).popover('show');
  },

  closePopover: function (evt) {
    $(evt.target).closest('.popover').siblings('.move_content').popover('hide');
    $(evt.target).closest('.popover').find(".text-error").addClass('hidden').html('');
  },

  handleEnter: function(evt) {
    if(evt.keyCode == 13){
      evt.preventDefault();
      this.changeOrder(evt);
    }
  },

  changeOrder: function(evt) {
    var position = $(evt.target).closest('form').find('.position').val();
    if (!position) {
      $(evt.target).closest('form').find(".text-error").removeClass('hidden').html("Position is required.");
      return;
    } else if (position < 1){
      $(evt.target).closest('form').find(".text-error").removeClass('hidden').html("Please enter valid value.");
      return;
    } else {
      $(evt.target).closest('form').find(".text-error").addClass('hidden');
    }

    var existing_position = $(evt.target).closest('.popover').siblings('.move_content').data('existing_position');
    if (position-1 === existing_position) {
      this.closePopover(evt);
      return;
    }

    var id = $(evt.target).closest('.popover').siblings('.move_content').data('question_id');

    $(evt.target).attr("disabled", true);

    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
    };
    var xhr = $.ajax({
      url: this.url,
      method: "POST",
      beforeSend: sendAuthentication,
      data: {
        id:id,
        position:position-1
      },
      dataType: "html",
      success: (data, textStatus, xhr) => {
        location.reload();
      },
      error: (xhr, textStatus, errorThrown) => {
        $(evt.target).closest('form').find(".text-error").removeClass('hidden').html("Error occured. Please try again.");
        $(evt.target).attr("disabled", false);
      }
    });
  },

  render: function () {
    this.$el.find('.move_content').popover({
      placement: 'top',
      html:true,
      content: MainTemplate,
      title: 'Move Question' + this.close_button,
      trigger: 'manual'
    });
  }
});
