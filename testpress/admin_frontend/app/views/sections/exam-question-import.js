import View from 'views/view';
import MainTemplate from 'templates/sections/exam-import-form';
import Select2View from 'views/select2-widget';
import {Exam} from 'models/exams';
import {ExamQuestionCollection} from 'models/exam_questions';
import QuestionBankListView from 'views/sections/question-bank-list';

export default View.extend(
{
  template: MainTemplate,

  events: {
    'select2:select #exam': 'examChanged',
    'select2:select #exclude_exam': 'excludeExamChanged',
    'select2:unselect #exam': 'examChanged',
    'select2:unselect #exclude_exam': 'excludeExamChanged',
    'click #add-exam-question': 'addSelectedQuestions'
  },

  subscribe: {
    'question-toggled': 'toggleAddButton',
  },

  initialize: function(options) {
    this.exam = options.exam;
    this.section = options.section;
    this.question_pool_id = options.question_pool_id;
    this.proxy_collection = new ExamQuestionCollection();
    this.initQuestionsList();
  },

  initQuestionsList: function() {
    this.questionBankList = new QuestionBankListView({exclude_exam: this.exam});
    this.subviews.questionBankList = this.questionBankList;
  },

  toggleAddButton: function() {
    if ($('input[name="questions"]:checked').length > 0) {
      this.$el.find('#add-exam-question').removeClass('hidden');
    } else {
      this.$el.find('#add-exam-question').addClass('hidden');
    }
  },

  examChanged: function(evt) {
    this.questionBankList.setExamIds(this.$el.find('[name="exam"]').val());
  },

  excludeExamChanged: function(evt) {
    let exclude_ids = [...this.$el.find('[name="exclude_exam"]').val(),this.exam.id]
    this.questionBankList.setExcludeExamIds(exclude_ids);
  },

  addSelectedQuestions: function(evt) {
    evt.preventDefault();
    var urls = this.questionBankList.selectedQuestions.pluck('url');
    _.each(urls, (item, index) => {
      this.proxy_collection.add({
        'exam': this.exam.get('url'),
        'section': this.section.get('url'),
        'question': item,
        'adaptive_exam_question_pool': this.question_pool_id
      });
    });

    this.proxy_collection.save({
      success: (model, xhr, status) => {
        window.location.reload();
      }
    });
  },

  render: function() {
    this.$el.html(this.template());
    this.exam_select_view = new Select2View({
        el: this.$el.find("#exam"),
        multiple: true,
        url: '/api/v2.2/admin/exams/',
        templateSelection: function(exam){
          return exam.title || exam.text;
        }
    });
    this.exclude_exam_select_view = new Select2View({
      el: this.$el.find("#exclude_exam"),
      multiple: true,
      url: '/api/v2.2/admin/exams/',
      templateSelection: function(exam){
        return exam.title || exam.text;
      }
    });
    this.subviews['exam_select_view'] = this.exam_select_view;
    this.subviews['exclude_exam_select_view'] = this.exclude_exam_select_view;
    this.$el.append(this.questionBankList.render().el);
    this.redelegate();
    return this;
  }
});
