import View from 'views/view';
import CommentsItemView from 'views/discussions/comments/item';
import CommentsReplyView from 'views/discussions/comments/reply';
import CKEditor from 'utils/editors/ckeditor';

import CommentsListTemplate from 'templates/discussions/comments/list';

export default View.extend(
{
  name: 'comments-list',

  className: 'comments-list',

  template: CommentsListTemplate,

  initialize: function(options) {
    this.initCommentsReply();
    this.listenTo(this.collection, 'add', this.renderItem);
    this.listenTo(this.collection, 'render-comments', this.renderComments);
    this.listenTo(this.collection, 'clear_marked', this.clearMarked);
  },

  clearMarked: function() {
    this.collection.forEach(function(model, index) {
      model.set({'accepted_answer': false});
    });
  },

  initCommentsReply: function() {
    this.reply = new CommentsReplyView({
      collection: this.collection,
      editor: new CKEditor('editor')
    });
    this.subviews.reply = this.reply;
  },

  renderItem: function(item) {
    var itemView = new CommentsItemView({
      model: item,
    });
    this.$el.find('.items').append(itemView.render().el)
    this.subviews[item.id] = itemView;
  },

  renderComments: function() {
    this.$el.find('.items').html('');
    this.collection.each((item) => {
      this.renderItem(item);
    });
  },

  render: function() {
    var self = this;
    this.$el.html(this.template());
    this.$el.find('.reply-container').append(this.reply.render().el);
    this.renderComments();
    return this;
  },

});
