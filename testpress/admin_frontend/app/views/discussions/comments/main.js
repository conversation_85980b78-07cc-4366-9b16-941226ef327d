import config from 'config';
import {getQueryVariable} from 'utils/functions';
import View from 'views/view';
import CommentsListView from 'views/discussions/comments/list';


export default View.extend(
{

  className: 'comment discussions',

  initialize: function(options) {
    this.options = _.defaults(options || {}, this.options);
    this.initList();
    this.more_el = $('<div class="load-more"><span class="comment-icon"></span>View more comments</div>');
  },

  events: {
    'click .load-more': 'viewMoreComments',
  },

  initList: function() {
    this.list = new CommentsListView({
      collection: this.collection,
    });
    this.subviews.list = this.list;
  },

  viewMoreComments: function(evt) {
    if (this.collection.next !== null) {
      this.$el.find('.load-more').remove();
      var page = getQueryVariable(this.collection.next, 'page');
      this.fetchComments({'page': page});
    }
  },

  fetchComments: function(data) {
    this.collection.fetch({
      data: data,
      remove: false,
      success: (collection, xhr, options) => {
        this.toggleMoreComments();
      },
    });
  },

  toggleMoreComments: function() {
    if (this.collection.next !== null) {
      this.more_el.insertBefore(this.$el.find('.reply'));
    } else {
      this.$el.find('.load-more').remove();
    }
  },

  render: function() {
    this.$el.html(this.list.render().el);
    if (this.collection.next !== null) {
      this.more_el.insertBefore(this.$el.find('.reply'));
    }
    return this;
  },
});

