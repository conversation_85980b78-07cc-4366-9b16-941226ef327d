import CommentsView from 'views/discussions/comments/main';

export var CommentsMixin = (
{
  initComments: function() {
    this.comments = new CommentsView({
      collection: this.comments_collection,
    });
    this.comments_collection.trigger('fetch-comments');
    this.subviews['comments'] = this.comments;
    this.listenTo(this.comments_collection, 'add', this.addCount);
    this.listenTo(this.comments_collection, 'sync', this.updateCount);
  },

  updateCount: function(data) {
    this.$el.find('.comment-heading .badge').text(data.count);
  },

  addCount: function(data) {
    var total_comments = parseInt(this.$el.find('.comment-heading .badge').text()) + 1;
    this.$el.find('.comment-heading .badge').text(total_comments);
  },

  renderComments: function() {
    this.comments.setElement(this.$el.find('.comments')).render();
  },
});
