import View from 'views/view';
import me from 'me';
import CommentsReplyTemplate from 'templates/discussions/comments/reply';


var Comments = require('models/comments');


export default View.extend(
{
  template: CommentsReplyTemplate,

  className: "reply",

  name: 'comments-reply',

  events: {
    'click .submit': 'submit',
  },

  initialize: function(options) {
    this.editor = options.editor;
    this.listenTo(this.editor.bus, 'submit', this.submit);
  },

  render: function() {
    var image_url = me.user_photo
    if(!image_url) {
      image_url = "https://media.testpress.in/static/img/default-user-pic.svg"
    }
    this.$el.html(this.template({'image': image_url}));
    _.defer(() => {
      this.editor.initialize();
    });
    return this;
  },

  hide: function() {
    this.$el.hide();
  },

  show: function() {
    this.$el.show();
  },

  submit: function() {
    var comment_text = this.editor.getData();
    comment_text = $.trim(comment_text);

    if (_.isEmpty(comment_text)) return;

    this.$el.find('.submit .loading').removeClass('hidden');
    var attrs = {
      'comment': comment_text,
      'content_object': this.collection.content_object,
    };

    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', Cookies.get('csrftoken'));
    };

    var options = {
      beforeSend: sendAuthentication,
      wait: true,
      success: (model, xhr, options) => {
        // Cleanup editor 
        this.editor.clearData();
        this.$el.find('.submit .loading').addClass('hidden');
        this.collection.add(model); //model should be added to collection only after model save success.
      },
      error: (function(model, xhr, options) {
        this.$el.find('.submit .loading').addClass('hidden');
        model.set('error', true);
      }).bind(this)
    }

    var model = new Comments.model(attrs);
    model.save([], options);
  },
});
