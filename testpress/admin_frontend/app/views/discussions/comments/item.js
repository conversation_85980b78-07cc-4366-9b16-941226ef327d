import View from 'views/view';
import VoteView from 'views/votes';
import CommentsItemTemplate from 'templates/discussions/comments/item';
import {commonHttpHeaders} from 'views/utils/functions';
import config from 'config';


export default View.extend(
{
  name: 'comment-item',

  template: CommentsItemTemplate,

  className: 'comment-item',

  events: {
    'click .accept_answer': 'acceptAnswer',
    'click .remove_answer': 'removeAnswer'
  },

  initialize: function(options) {
    this.listenTo(this.model, 'remove', this.remove);
    this.listenTo(this.model, 'change', this.render);
    if (config.institute.comments_voting_enabled) {
      this.initVoteView();
    }
  },

  initVoteView: function() {
    this.vote_view = new VoteView({
      model: this.model,
    });
    this.subviews.vote_view = this.vote_view;
  },

  acceptAnswer: function () {
    this.showLoading();
    const forumId = this.model.get("content_object").id;
    fetch(`/api/v2.5/discussions/${forumId}/comments/mark_as_answer/`, {
      method: 'post',
      credentials: 'same-origin',
      headers: commonHttpHeaders,
      body: JSON.stringify({
        comment_id: this.model.id
      })
    }).then((response) => {
    this.hideLoading();
     if (response.ok) {
      this.model.collection.trigger('clear_marked');
      this.model.set({'accepted_answer': true});
      this.model.collection.trigger('render-comments');
     }
    });
  },

  showLoading: function() {
    this.$el.find('.spinner1').removeClass('hidden');
  },

  hideLoading: function() {
    this.$el.find('.spinner1').addClass('hidden');
  },

  removeAnswer: function () {
    this.showLoading();
    this.$el.find('.spinner1').removeClass('hidden');
    const forumId = this.model.get("content_object").id
    fetch(`/api/v2.5/discussions/${forumId}/comments/unmark_as_answer/`, {
      method: 'post',
      credentials: 'same-origin',
      headers: commonHttpHeaders
    }).then((response) => {
      this.hideLoading();
      if (response.ok) {
        this.model.set({'accepted_answer': false});
        this.model.collection.trigger('render-comments', this.model);
      }
    });
  },

  getContext: function() {
    let context = this.model.toJSON();
    let created_date = new Date(this.model.get('created')).setHours(0,0,0,0);
    let full_date = moment(this.model.get('created')).format('hh:mm a, DD MMM YYYY');
    let today = new Date().setHours(0,0,0,0);
    let time = moment( new Date(this.model.get('created')),
      "DD/MM/YYYY HH:mm:ss"
    ).fromNow();

    if (created_date != today) {
      time = full_date;
    }

    context['time'] = time;
    context['full_date'] = full_date;

    return context
  },

  render: function() {
    this.$el.html(this.template(this.getContext()));
    if (this.model.get("accepted_answer")) {
      this.$el.addClass("accepted_answer")
    }
    if (this.vote_view) {
      this.vote_view.setElement(this.$el.find('.votes')).render();
    }
    return this;
  }
});
