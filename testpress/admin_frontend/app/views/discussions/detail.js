import View from 'views/view';
import {CommentsMixin} from 'views/discussions/comments/mixin'
import MainTemplate from 'templates/discussions/detail'
import config from 'config';


export default View.extend(
{
  template: MainTemplate,

  className: 'gray-page course-discussions clearfix',

  initialize: function(options) {
    _.extend(this, CommentsMixin);
    this.options = options;
    this.comments_collection = options.comments_collection;
    this.initComments();
    this.course = options.course;
  },

  render: function() {
    var context = this.model.toJSON();
    let when = moment(
      new Date(this.model.get('created')),
      "DD/MM/YYYY HH:mm:ss").fromNow();
    context['when'] = when;
    context['author_url'] = this.options.author_url;
    context['edit_url'] = this.options.edit_url;
    context['delete_url'] = this.options.delete_url;
    context['when_alt'] = moment.utc(this.model.get('created')).format("DD MMM Y hh:mm z");
    this.$el.html(this.template(context));
    this.renderComments();
    return this;
  }
});
