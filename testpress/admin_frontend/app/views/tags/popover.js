import View from 'views/view';
import Select2View from 'views/select2-widget';

import ContentTemplate from 'templates/tags/content';
import TitleTemplate from 'templates/tags/title';

const CustomSelectionAdapter = $.fn.select2.amd.require("select2/selection/customSelectionAdapter");
const sendAuthentication = function(xhr) {
  xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
};


export default View.extend(
{
  initialize: function(options) {
    this.tags = options.tags;
    this.update_url = options.update_url;
  },

  render: function() {
    this.renderPopOver();
    this.$el.popover('show');
    const select2View = this.attachSelect2();
    this.initializeSelect2Listeners(select2View)
  },

  renderPopOver: function() {
    this.$el.popover({
      placement: 'top',
      html:true,
      content: ContentTemplate({tags: this.tags}),
      title: TitleTemplate(),
      trigger: 'manual'
    });
  },

  attachSelect2: function() {
    const select = this.$el.siblings('.popover').find('select');
    return new Select2View({
      el: select,
      url: '/api/v2.5/admin/tags/',
      templateSelection: function (tag) { return tag.name || tag.text; },
      templateResult: function (tag) { return tag.name || tag.text; },
      width: '150px',
      searchParam: 'search',
      tags: true,
      tokenSeparators: [","],
      selectionAdapter: CustomSelectionAdapter
    });
  },

  initializeSelect2Listeners: function(select2View) {
    select2View.$el.on('select2:unselect', this.updateTags.bind(this));
    select2View.$el.on('select2:select', this.updateTags.bind(this));
  },

  updateTags: function(evt) {
    let tags = $(evt.target).select2('data');
    tags = tags.map((tag) => tag.name || tag.text);

    $.ajax({
      url: this.update_url,
      method: "PATCH",
      data: JSON.stringify({"tags": tags}),
      beforeSend: sendAuthentication,
      dataType: 'json',
      contentType:"application/json; charset=utf-8",
      success: (data) => {
        this.$el.data("tags", data.tags.toString())
      }
    })
  },

  remove: function() {
    this.$el.popover('destroy');
  },
})
