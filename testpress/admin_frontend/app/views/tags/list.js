import View from 'views/view';
import TagsPopoverView from 'views/tags/popover';

export default View.extend(
{
  events: {
    'click .tags': 'showTags',
    'click .close': 'closePopovers'
  },

  showTags: function (evt) {
    evt.stopPropagation();
    this.closePopovers();
    const tagsPopoverView = this.initializePopover(evt.target)
    tagsPopoverView.render();
    this.subviews["tags"] = tagsPopoverView;
    this.addClickOutsideListener();
  },

  closePopovers: function () {
    _.invoke(this.subviews, 'remove');
  },

  initializePopover: function (el) {
    return  new TagsPopoverView({
      tags: $(el).data('tags').split(',').filter(Boolean),
      el: $(el),
      update_url: $(el).data("tag_update_url")
    });
  },

  addClickOutsideListener: function() {
    const self = this;
    $('body').on("click", function(evt){
      const clickedOutsidePopover = $(".popover").has(evt.target).length == 0;
      if (clickedOutsidePopover) {
        self.closePopovers();
      };
    });
  }
})
