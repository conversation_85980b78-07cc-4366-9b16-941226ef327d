import View from "views/view";
import RowTemplate from "templates/sections-analytics/row.jst"

export const RowView = View.extend({
  tagName: 'tr',

  initialize: function (options) {
    this.model = options.model;
    this.show_score_column = options.show_score_column
    this.showPartialCorrectStats = options.showPartialCorrectStats;
    let questions_count = (
      this.model.get("correct_answers_count")
      + this.model.get("incorrect_answers_count")
      + this.model.get("unanswered_count")
      + this.model.get("partial_correct_answers_count")
    );
    this.model.set("questions_count", questions_count, { silent: true });
  },

  template: RowTemplate,

  render: function () {
    const templateData = Object.assign(this.model.toJSON(), {
        show_score_column: this.show_score_column,
        showPartialCorrectStats: this.showPartialCorrectStats,
    });
    this.$el.html(this.template(templateData));
    return this;
  }
});
