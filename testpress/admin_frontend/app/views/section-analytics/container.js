import View from "views/view";
import {TableView} from "views/section-analytics/table";
import {Bar<PERSON>hartView} from "views/section-analytics/bar_chart";
import {Bar<PERSON><PERSON> as BarChartClass} from "utils/charts/echart";
import ContainerTemplate from "templates/sections-analytics/container.jst";

export const ContainerView = View.extend({
  template: ContainerTemplate,

  initialize: function (options) {
    this.collection = options.collection;
    this.userexam = options.userexam
  },

  renderTable: function () {
    this.subviews['tableView'] = new TableView({
      el: this.$el.find('.section-table'),
      collection: this.collection,
      userexam: this.userexam,
    }).render();
  },

  renderChart: function() {
    this.subviews['chartView'] = new BarChartView({
      el: this.$el.find('.section-bar-chart'),
      chartClass: BarChartClass,
      collection: this.collection,
    }).render();
  },

  render: function () {
    this.$el.html(this.template());
    this.renderTable();
    this.renderChart();
    return this;
  }
});
