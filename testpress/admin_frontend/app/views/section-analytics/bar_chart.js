import View from "views/view";
import ChartTemplate from "templates/sections-analytics/chart.jst"

export const BarChartView = View.extend({
  template: ChartTemplate,

  initialize: function(options) {
    this.collection = options.collection;
    this.chartClass = options.chartClass;
    this.has_partial_correct_stats = this.hasPartialCorrectStats();
    this.initializeChart();
  },

  hasPartialCorrectStats: function() {
    return Boolean(_.find(this.collection.models, (section) => section.get("partial_correct_answers_count") != 0));
  },

  initializeChart: function() {
    this.chart = new this.chartClass();
    this.chart.setBarsWithColors(this.getBarColors());
    this.collection.each((section) => {
      this.chart.setDataSet(section.get('info').name, this.getChartDataForSection(section));
    });
  },

  getBarColors: function() {
    let colorsData = {
      "Total Questions": "rgb(109,165,255)",
      "Correct Attempts": "rgb(63,96,43)",
      "Partial Correct": "rgb(150,206,180)",
      "Incorrect Attempts": "rgb(255,111,105)",
      "Unattempted": "rgb(255,204,92)",
    }
    if (!this.has_partial_correct_stats) {
      delete colorsData["Partial Correct"];
    }
    return colorsData;
  },

  getChartDataForSection: function(section) {
    let chartData = {
      "Total Questions": section.get('questions_count'),
      "Correct Attempts": section.get('correct_answers_count'),
      "Partial Correct": section.get("partial_correct_answers_count"),
      "Incorrect Attempts": section.get('incorrect_answers_count'),
      "Unattempted": section.get('unanswered_count'),
    };
    if (!this.has_partial_correct_stats) {
      delete chartData["Partial Correct"];
    }
    return chartData;
  },

  render: function () {
    this.$el.html(this.template());
    let $chart = this.$el.find('#chart')[0];
    let $legend = this.$el.find('.chart-legend')[0];
    this.chart.generateChart($chart);
    this.chart.generateLegend($legend);
    return this;
  }
});
