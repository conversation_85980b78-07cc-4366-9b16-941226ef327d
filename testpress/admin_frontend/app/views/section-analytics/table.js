import Model from "models/model";
import View from "views/view";
import {RowView} from 'views/section-analytics/row';
import TableTemplate from "templates/sections-analytics/table.jst";

const SAT_TEMPLATE = 16


export const TableView = View.extend({
  template: TableTemplate,

  initialize: function(options) {
    this.collection = options.collection;
    this.userexam = options.userexam
    this.show_score_column= true
    this.showPartialCorrectStats = this.hasPartialCorrectStats();
  },

  hasPartialCorrectStats: function() {
    return Boolean(_.find(this.collection.models, (section) => section.get("partial_correct_answers_count") != 0));
  },

  renderRow: function(section) {
    let rowView = new RowView({
      model: section,
      show_score_column: this.show_score_column,
      showPartialCorrectStats: this.showPartialCorrectStats,
    });
    this.$el.find('tbody').append(rowView.render().el);
    this.subviews[section.get('id')] = rowView;
  },

  renderTotalRow: function() {
    let correct_answers_count = 0, incorrect_answers_count = 0, unanswered_count = 0;
    let partial_correct_answers_count=0;

    this.collection.each((section) => {
      correct_answers_count += section.get("correct_answers_count");
      incorrect_answers_count += section.get("incorrect_answers_count");
      unanswered_count += section.get("unanswered_count");
      partial_correct_answers_count += section.get("partial_correct_answers_count");
    });
    const proxyModel = new Model({
      info: {
        name: "Total",
      },
      correct_answers_count,
      incorrect_answers_count,
      unanswered_count,
      score: this.userexam.get("score"),
      partial_correct_answers_count,
    });
    this.renderRow(proxyModel);
  },

  render: function () {
    this.$el.html(
      this.template({
        show_score_column: this.show_score_column,
        showPartialCorrectStats: this.showPartialCorrectStats,
      })
    );
    this.collection.each((section) => {
      this.renderRow(section);
    });
    this.renderTotalRow();
    return this;
  },
});
