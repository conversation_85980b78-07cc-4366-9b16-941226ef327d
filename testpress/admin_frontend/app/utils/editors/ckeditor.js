import BaseEditor from 'utils/editors/base';
import config from 'config';


export default class CKEditor extends BaseEditor {

    initialize() {
        let options = this.options || this.getDefaultOptions();
        this.editor = CKEDITOR.replace(this.selector, options);
    }

    wrapWithCustomFont(font_name, font_path) {
        var font_face = `@font-face {font-family: ${font_name};src: url(${font_path});}`;
        this.editor.config.contentsCss = `${font_face} body{font-family:${font_name};}` + this.editor.config.contentsCss;
    }

    setData(data) {
        this.editor.setData(data);
    }

    getData(data) {
        return this.editor.getData();
    }

    clearData() {
        this.editor.setData('');
    }

    getDefaultOptions() {
        return {
            toolbarGroups: [
                {name: 'document', groups: ['mode', 'document', 'doctools']},
                {name: 'clipboard', groups: ['clipboard', 'undo']},
                {name: 'editing', groups: ['find', 'selection', 'spellchecker', 'editing']},
                {name: 'forms', groups: ['forms']},
                {name: 'basicstyles', groups: ['basicstyles', 'cleanup']},
                {name: 'paragraph', groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph']},
                {name: 'links', groups: ['links']},
                {name: 'insert', groups: ['insert']},
                {name: 'styles', groups: ['styles']},
                {name: 'colors', groups: ['colors']},
                {name: 'tools', groups: ['tools']},
                {name: 'others', groups: ['others']},
                {name: 'about', groups: ['about']}
            ],
            height: 100,
            removeButtons: 'Save,NewPage,Preview,Print,Templates,CopyFormatting,RemoveFormat,Subscript,Superscript,' +
                'Unlink,Anchor,Flash,PageBreak,Smiley,SpecialChar,Iframe,ShowBlocks,',
            extraPlugins: 'mathjax,uploadimage,image2,colordialog,tableresize,quicktable,uploadfile,html5audio',
            uploadUrl: '/api/v2.2/image_upload/?editor=ckeditor',
            filebrowserUploadUrl: '/api/v2.2/ck_image_upload/',
            filebrowserImageUploadUrl: '/api/v2.2/ck_image_upload/',
            mathJaxLib: config.static_url + 'js/MathJax-2.7.0/MathJax.js?config=TeX-AMS_HTML',
            qtRows: 8,
            qtColumns: 10,
            qtBorder: '1',
            qtWidth: '90%',
            qtStyle: {'border-collapse': 'collapse'},
            qtClass: 'test',
            qtCellPadding: '0',
            qtCellSpacing: '0'
        }
    }
}
