export default class BaseEditor {
  constructor(selector, options) {
    this.subviews = {};
    this.selector = selector;
    this.options = options;
    this.bus = _.extend({}, Backbone.Events);
  }

  initialize() {}

  setData(data) {
    throw new Error("Method 'setData()' must be implemented.");
  }

  wrapWithCustomFont() {}

  getData(data) {
    throw new Error("Method 'getData()' must be implemented.");
  }

  clearData() {
    throw new Error("Method 'clearData()' must be implemented.");
  }
}
