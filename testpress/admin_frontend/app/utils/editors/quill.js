import Quill from 'quill';
import BaseEditor from 'utils/editors/base';

export default class QuillEditor extends BaseEditor {

  initialize() {
    let options = this.options || this.getDefaultOptions();
    this.editor = new Quill(this.selector, options);
  }

  setData(data) {
    this.editor.setContents([{ insert: data }]);
  }

  getData(data) {
    return this.editor.root.innerHTML.split('  ').join(' &nbsp;');
  }

  clearData() {
    this.editor.setContents([{ insert: '\n' }]);
  }

  getDefaultOptions() {
    let toolbar_options = [
      ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
      ['blockquote', 'code-block'],

      [{ 'header': 1 }, { 'header': 2 }],               // custom button values
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
      [{ 'direction': 'rtl' }],                         // text direction

      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [ 'link', 'image', 'formula' ],          // add's image support
      [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
      [{ 'font': [] }],
      [{ 'align': [] }],

      ['clean']                                         // remove formatting button
    ];
    return {
      bounds: '#editor',
      theme: 'snow',
      placeholder: 'Leave a comment...',
      modules: {
        toolbar: toolbar_options,
        keyboard: {
          bindings: [{
            key: 13,
            metaKey: true,
            handler: () => {
              this.bus.trigger('submit');
            },
          }, {
            key: 13,
            ctrlKey: true,
            handler: function() {
              this.bus.trigger('submit');
            },
          }]
        },
      },
    } // editor options
  }
}
