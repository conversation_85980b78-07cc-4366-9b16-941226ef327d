import BaseBar<PERSON>hart from './base';
const ECharts = require('echarts/lib/echarts');
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

class BarChart extends BaseBarChart {
  getData(barName){
    let data = [];
    Object.keys(this.dataSets).map((key) => {
      data.push(this.dataSets[key][barName])
    });
    return data;
  }


  generateChart(selector) {
    this.chart = new ECharts.init(selector);
    let barNames = Object.keys(this.barsWithColors);
    let dataSets = [];

    barNames.forEach((barName) => {
      dataSets.push({
        name: barName,
        type: 'bar',
        data: this.getData(barName),
        color: this.barsWithColors[barName],
      })
    });
    this.options = {
      tooltip: {},
      xAxis: {
        data: Object.keys(this.dataSets),
      },
      legend: {
        data: barNames,
      },
      yAxis: {
        type: 'value'
      },
      series: dataSets,
    };
    this.chart.setOption(this.options);
  }

  generateLegend(selector) {
    this.options['legend'] = {
      data: Object.keys(this.barsWithColors)
    };
    this.chart.setOption(this.options);
  }
}

export {BarChart};
