export default class BaseBarChart {
  constructor(options) {
    this.barsWithColors = {};
    this.dataSets = {};
    this.options = options;
  }

  /**
   * Set the bar names and corresponding colors.
   * @param {Object.<string, string>} data - Dict barName: Color pairs.
   */
  setBarsWithColors(data) {
    this.barsWithColors = data;
  }

  /**
   * Set the dataSetName and data.
   * @param {string} dataSetName - Name of the dataset that displays in x axis.
   * @param {Object.<string, number>} data - Dict barName: Value pairs.
   */
  setDataSet(dataSetName, data) {
    this.dataSets[dataSetName] = data;
  }

  generateChart(selector){
    throw new Error("Method 'generateChart()' must be implemented.");
  }

  generateLegend(selector){
    throw new Error("Method 'generateLegend()' must be implemented.");
  }
}
