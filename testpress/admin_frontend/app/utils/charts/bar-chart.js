import BaseBarChart from './base'


class Bar<PERSON><PERSON> extends BaseBarChart {
  getData(barName){
    let data = [];
    Object.keys(this.dataSets).map((key) => {
      data.push(this.dataSets[key][barName])
    });
    return data;
  }


  generateChart(selector) {
    let labels = Object.keys(this.dataSets);
    let barNames = Object.keys(this.barsWithColors);
    let dataSets = [];

    barNames.forEach((barName) => {
      dataSets.push({
        label: barName,
        fillColor: this.barsWithColors[barName],
        strokeColor: this.barsWithColors[barName],
        pointStrokeColor: "#fff",
        pointHighlightFill: "#fff",
        data: this.getData(barName),
      })
    });

    this.chart = new Chart(selector).Bar(
      { labels: labels, datasets: dataSets },
      { multiTooltipTemplate: "<%if (datasetLabel){%><%=datasetLabel%>: <%}%><%= value %>" }
    );
  }

  generateLegend(selector) {
    selector.html(this.chart.generateLegend());
  }
}

export {BarChart};
