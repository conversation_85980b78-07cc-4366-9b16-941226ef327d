
export var getQueryVariable = function(url, variable) {
  var queryString = url.substring( url.indexOf('?') + 1 );
  var vars = queryString.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (decodeURIComponent(pair[0]) == variable) {
        return decodeURIComponent(pair[1]);
    }
  }
}

export var getStorageRelativePath = function(url){
  var path = new URL(url).pathname;
  var relative_path = path.substring(path.indexOf("/institute/")+1);
  return relative_path;
}


