var urlpatterns = {
    "running_stats":                "/exams/running/<exam_slug>/stats/",
    "completed_stats":              "/exams/completed/<exam_slug>/stats/",
    "exams_update":                 "/exams/<exam_slug>/update/",
    "exams_running":                "/exams/running/",
    "exams_scheduled":              "/exams/scheduled/",
    "exams_completed":              "/exams/completed/",
    "create_exam":                  "/exams/create/",
    "create_exam_set":              "/exams/create/?qs=<set_id>",
    "question_set_detail":          "/set/<set_id>/",
    'question_set_print':           "/set/<set_id>/print/",
    'question_set_update':          "/set/<set_id>/update/",
    'questions_add':                "/set/<set_id>/add/",
    "questions_detail":             "/set/<set_id>/<question_id>/update/",
    "members_index":                "/members/",
    "members_detail":               "/members/<member_id>/",
    "index":                        "/",
    "settings_index":               "/settings/profile/",
    "settings_institute":           "/settings/institute/",
    "logout":                       "/logout/",
    "batches_detail":               "/settings/batches/<batch_id>/",
    "profile":                      "/user/",
    "exam_admin_url":               "/exams/<id>/stats/",
    "userexam_url":                 "/exams/run/<exam_slug>/start/<userexam_id>/",
    "exam_ranks_url":               "/exams/<slug>/",
    "exam_review_ranks_url":        "/exams/review/<userexam_id>/ranks/",
    "documents_index":              "/documents/",
    "courses_index":                "/courses/",
    "analytics_subjects":           "/analytics/subjects/",
    "questions_index":              "/questions/",
    "market_index":                 "/market/",
    "notes_detail":                 "/documents/<slug>/",
    "notes_download":               "/documents/<id>/download/",
    "topic_detail":                 "/documents/<slug>/<topic_slug>/",
    "courses_chapters_null_parent": "/courses/<slug>/",
    "courses_chapters":             "/courses/<slug>/<chapter_slug>",
    "courses_contents":             "/courses/chapter/<chapter_slug>/contents/",
    "course_content":               "/courses/content/<content_id>"
};

var reverse = function(name, kwargs) {
  var url = urlpatterns[name] || false;
  if (!url) {
    return false;
    //throw('URL not found for view: ' + name);
  }
  var _url = url;
  var key;
  for (key in kwargs) {
    if (kwargs.hasOwnProperty(key)) {
      if (!url.match('<' + key +'>')) {
        return false;
        //throw(key + ' does not exist in ' + _url);
      }
      url = url.replace('<' + key +'>', kwargs[key]);
    }
  }
  
  var re = new RegExp('<[a-zA-Z0-9-_]{1,}>', 'g');
  var missing_args = url.match(re);
  if (missing_args) {
    return false;
    //throw('Missing arguments (' + missing_args.join(", ") + ') for url ' + _url);
  }
  return url;
};

module.exports = reverse;

