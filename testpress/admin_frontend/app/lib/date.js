
var month_names = [];
month_names[month_names.length] = "Jan. ";
month_names[month_names.length] = "Feb. ";
month_names[month_names.length] = "Mar. ";
month_names[month_names.length] = "Apr. ";
month_names[month_names.length] = "May. ";
month_names[month_names.length] = "Jun. ";
month_names[month_names.length] = "Jul. ";
month_names[month_names.length] = "Aug. ";
month_names[month_names.length] = "Sept. ";
month_names[month_names.length] = "Oct. ";
month_names[month_names.length] = "Nov. ";
month_names[month_names.length] = "Dec. ";

var day_names = [];
day_names[day_names.length] = "Sunday";
day_names[day_names.length] = "Monday";
day_names[day_names.length] = "Tuesday";
day_names[day_names.length] = "Wednesday";
day_names[day_names.length] = "Thursday";
day_names[day_names.length] = "Friday";
day_names[day_names.length] = "Saturday";

var timeNow = function(date){
  return ((date.getHours() < 10)?"0":"") +
         ((date.getHours()>12)?(date.getHours()-12):date.getHours()) +
         ":"+
         ((date.getMinutes() < 10)?"0":"") +
         date.getMinutes() +":"+
         ((date.getSeconds() < 10)?"0":"") +
         date.getSeconds() + ((date.getHours()>12)?(' PM'):' AM');
};

module.exports = function(date) {
    date = new Date(date);
    return month_names[date.getMonth()] + date.getDate() + ', ' + date.getFullYear() + ', ' + timeNow(date);
};
