.filter-title {
  display: inline-block;

  button {
    font-size: inherit;
    border: none;
    background: inherit;
    line-height: inherit;

    &:focus {
      outline: none;
    }
  }

  .title {
    font-size: inherit;
  }

  .caret {
    vertical-align: middle;
    margin-top: 0px;
    margin-left: 4px;
  }

  .dropdown-menu {
    border-radius: 2px;

    li {
      font-size: 14px;
    }
  }
}


.tags-list {
  .popover {
    max-width: 450px;
    border-radius: 3px;
    border: 0px;
    padding: 10px 6px;
    box-shadow: 0 1px 7px rgba(0, 0, 0, 0.2) !important;

    .popover-content {
      width: 400px;
    }

    .select2-selection__rendered li{
      padding: 0;
      padding-right: 10px;
    }

    .select2-search--inline {
      border-bottom: none;
    }

    .select2-container--custom-selection {
      width: 100%;

      .select2-selection {
        border: none;
      }
    }

    .select2-selection.select2-selection--multiple {
      margin: 0;
    }

    .popover-title {
      background: #fff;
      font-weight: bold;
      border-bottom: none;
    }

    .selection {
      margin: 0px !important;

      .select2-selection.select2-selection--multiple.select2-selection--multiple--custom {
        height: 35px;
        background: #F9F9F9;
        border-color: #e5e5e5;
      }
    }

    .select2-selection.select2-selection--multiple.select2-selection--multiple--custom {
      margin: 0 !important;
    }

    .select2.select2-container.select2-container--default {
      margin: 0 !important;
    }


    .select2-search.select2-search--inline {
      position: relative;
      bottom: -10px;
      padding: 0 !important;
    }

    .select2-selection__rendered {
      padding: 0px !important;
    }

    .dropdown-wrapper {
      margin: 0;
      display: none;
    }

    .select2-search__field {
      left: 5px;
      position: relative;
    }

    .select2-selection__clear {
      display: none;
    }

    .select2-selection__choice {
      background: #F2F2F2 !important;
      border: none !important;
      padding: 2px 9px 2px 6px!important;
      color: #333;
    }
  }
}

.select2-selection.select2-selection--multiple{
    max-height: 200px;
    overflow-y: scroll;
}
