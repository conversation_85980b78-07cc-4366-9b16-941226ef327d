.import-data {

  #id_1-very_easy,
  #id_1-easy,
  #id_1-medium,
  #id_1-hard,
  #id_1-very_hard {
    width: 300px;
  }

  .helptext {
    color: #666666;
  }

  .stepBar {
    position: relative;
    list-style: none;
    margin: 0 0 1em;
    padding: 0;
    text-align: center;
    width: 100%;
    overflow: hidden;
    margin-bottom: 20px;
    *zoom: 1;

    .step {
      position: relative;
      float: left;
      width: 20%;

      display: inline-block;
      line-height: 30px;
      padding: 0 40px 0 20px;
      background-color: #eee;
      -moz-box-sizing: border-box;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;

      &:before,
      &:after {
        position: absolute;
        left: -15px;
        display: block;
        content: '';
        background-color: #eee;
        border-left: 4px solid #FFF;
        width: 20px;
        height: 15px;
      }

      &:after {
        top: 0;
        -moz-transform: skew(30deg);
        -ms-transform: skew(30deg);
        -webkit-transform: skew(30deg);
        transform: skew(30deg);
      }

      &:before {
        bottom: 0;
        -moz-transform: skew(-30deg);
        -ms-transform: skew(-30deg);
        -webkit-transform: skew(-30deg);
        transform: skew(-30deg);
      }

      &:first-child {
        -moz-border-radius-topleft: 4px;
        -webkit-border-top-left-radius: 4px;
        border-top-left-radius: 4px;
        -moz-border-radius-bottomleft: 4px;
        -webkit-border-bottom-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:first-child:before,
      &:first-child:after {
        content: none;
      }

      &:last-child {
        -moz-border-radius-topright: 4px;
        -webkit-border-top-right-radius: 4px;
        border-top-right-radius: 4px;
        -moz-border-radius-bottomright: 4px;
        -webkit-border-bottom-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &.step1 {
        cursor: pointer;
      }

      &.step2 {
        cursor: default;
      }

      &.current {
        color: #FFF;
        background-color: #0099e0;

        &:before,
        &:after {
          background-color: #0099e0;
        }
      }
    }
  }
}
