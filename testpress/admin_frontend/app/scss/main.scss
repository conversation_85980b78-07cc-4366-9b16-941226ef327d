@import "mixins.scss";
@import "forms.scss";
@import "questions-form.scss";
@import "section-detail.scss";
@import "question-delete.scss";
@import "buttons.scss";
@import "misc.scss";
@import "notification.scss";
@import "sortable.scss";
@import "comments.scss";
@import "batches.scss";
@import "feedback_comments.scss";
@import "membership.scss";
@import "attempts-list.scss";
@import "sales_report.scss";
@import "grading.scss";
@import "attempts-detail.scss";
@import "bulk-add-questions";
@import "import-data";
@import "custom_fonts";
@import "question_folder";
@import "widgets";
@import "moderation_list";
@import "form_builder";
@import "spinner";
@import "course_discussions.scss";
@import "signup_form_builder";
@import "subjects_list";
@import "admin-question-list";
@import "alertbox";
@import "course_list";
@import "questions-set";
@import "bundles";
@import "review";
@import "ckeditor";

body {
  @include user-select(unset !important);
  @include touch-callout(unset !important);
}
