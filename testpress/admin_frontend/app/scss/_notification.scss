.notification {
  left: 50%;
  transform: translate(-50%, 0);
  position: fixed;
  font-size: 13px;
  line-height: 20px;
  text-align: center;
  top: 100px;

  .error {
    background-color: #de4343 !important;
    border: 1px solid #c43d3d !important; 
    border-radius: 2px;
    color: white;
    padding: 1px 15px;
    a {
      color: white !important;
      text-decoration: underline;
    }
  }

  .success {
    background-color: #f9edbe;
    padding: 1px 15px;
    color: #333;
    font-weight: bold;
    border-radius: 2px;
    a {
      color: #333 !important;
      text-decoration: underline;
    }
  }
}
