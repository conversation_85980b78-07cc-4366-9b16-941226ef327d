#sales-report-filters {
  padding-left: 5px;
  padding-right: 5px;

  #duration{
    border: 1px solid #ccc;
    background: #fff;
    height: 20px;
    padding: 2px 10px;

    i{
      margin-right: 3px;
    }
  }
}

#two-inputs .report-filters{
  height: 15px;
  width: 85px;
}

.sales-report {

  .report-filters{
    height: 25px;
    -webkit-appearance: none;
    width: 100%;
    border-radius: 0px;
    background: #fff;
    border-color: #ccc;
    padding: 4px 6px;
  }

  .text-right .exam-filter{
    padding-right: 0px;
  }
}

.sales-report.aggregates{
  margin: 20px 0;
}

.sales-report.aggregates .card{
  border: 1px solid #d9edf7;
  background: #d9edf7;
  color: #3a87ad;
  padding: 20px;
  margin: 10px 30px;
}

.report-total{
  border-right: 1px solid #ccc;
}

.count{
  font-size: 25px;
  line-height: normal;
  margin-bottom: 7px;
}

.subtitle{
  margin: 0px;
  color: #888;
  text-transform: uppercase;
  font-size: 12px;
}

.alert-grey{
  background: #F7F7F9;
  border: #F7F7F9;
  color: #333;
}

.sales-report.aggregates{
  margin: 20px 0;
}

.sales-report.aggregates .card{
  border: 1px solid #d9edf7;
  background: #d9edf7;
  color: #3a87ad;
  padding: 20px;
  margin: 10px 30px;
}

.alert-grey{
  background: #F7F7F9;
  border: #F7F7F9;
  color: #333;
}

.daterangepicker .calendar-table th, .daterangepicker .calendar-table td{
  padding: 0px !important;
}