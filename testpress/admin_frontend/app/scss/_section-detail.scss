$index-width: 25px;
.section-detail {
  p {
    margin: 0px;
  }

  .option {
    p {
      display: inline-block;
    }
    margin-bottom: 10px;
  }

  .control-group.callout {
    position: fixed;
    bottom: 0px;
    width: 35%;
    z-index: 100;
    background-color: white;
    padding: 0px;
    margin-bottom: 0px;

    .cta {
      float: right;
      position: relative;
      bottom: 1px;
      display: inline-block;
      font-weight: 400;
      line-height: 1.25;
      text-align: center;
      white-space: nowrap;
      vertical-align: middle;
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      border: 1px solid transparent;
      padding: .5rem 1rem;
      font-size: 1rem;
      border-radius: .25rem;
      color: #fff;
      background-color: #d9534f;
      border-color: #d9534f;
      padding: .75rem .5rem;
      font-size: .875rem;
      border-radius: .1rem;
      width: 100%;
      text-transform: uppercase;
      font-weight: bold;

	  i[class^="icon-"] {
		margin-right: 4px;
	  }
	}
  }
  .select-all {
    margin-right: 6px !important;
    position: relative;
    bottom: 4px;
  }

  .breadcrumb {
    padding-left: 0px;
  }

  .loader {
    img {
      display: block;
      margin: 0 auto;
      margin-top: 20%;
    }
  }

  .form-vertical {
    margin-left: 5px;
  }

  h4 {
    border-bottom: 1px solid #ddd;
    padding-bottom: 3px;
    padding-left: 5px;
    font-weight: normal;
  }

  .row-fluid {
    display: flex;
  }

  .left-pane {
    min-height: 500px;

    .btn-group,
    .btn-danger {
      margin-right: 10px;
    }

    .dropdown-menu {
      border-radius: 2px;
    }
  }

  .right-pane {
    margin-left: 0px;
  }

  .left-pane,
  .right-pane {
    flex: 1;

    input[name='filter_unused_questions'],
    .clear-filter {
      margin-left: 5px;
    }

    ul.questions-list {
      >li {
        border-bottom: 1px solid #E5E5E5;
        padding: 10px;
        padding-left: 0px;

        &.empty {
          border-bottom: none;
          text-align: center;

          .title {
            margin-top: 10%;
            font-size: 18px;
            line-height: 1.8;
          }
        }

        .index {
          color: #555;
          font-weight: bold;
  	      float: left;
          width: $index-width;

          input {
            display: block;
          }
        }

  	    .main {
  	      margin-left: $index-width;

          >.content {
            background-color: #f8f8f8;
            border-left: 2px solid #999999;
            display: block;
            margin-top: 4px;
            padding: 10px 15px;
            text-align: justify !important;
            word-wrap: break-word;

            .heading {
              font-weight: bold;
            }

            .nested-response {
              margin-top: 10px;
              margin-left: 15px;
            }

            .choices, .explanation, .subject, .correct-answer {
              margin-top: 10px;
              margin-bottom: 10px;

              .nested {
                margin-left: 20px;
              }

              .choice {
                margin-top: 10px;
              }
            }

            &.custom-font {
              text-align: left !important;
            }

            video {
              width: 100%;
            }
          }

          .actions {
            margin-top: 10px;

            a.mini.pull-right,
			span {
  			  margin-left: 10px;
			}

            a.mini.pull-right {
              margin-top: 1px;
            }
          }
  	    }

        table {
          td, th {
            border: 1px solid #999;
          }
        }
      }
    }
  }

  .questions-list-container {
    margin-left: 5px;
    margin-bottom: 50px;

    .select2-selection--multiple {
      height: 28px;
      min-height: 28px;
    }

    .tags .select2-selection--multiple {
      min-height: 32px;
    }

    input {
      height: 17px;
      min-height: 28px;
    }

    .form-vertical {
      margin-left: 0px;
    }

    #header {
      .wrap {
        .left {
          width: 50%;
          float: left;
          position: relative;
        }

        .right {
          width: 50%;
          float: right;
          position: relative;
          top: -2px;
          text-align: right;

          .select2 {
            text-align: left;
          }
        }
      }
    }

    form {
      margin-bottom: 0px;

      .row-fluid {
        margin-bottom: 10px;

        &.no-margin {
          margin-bottom: 0px;
        }
      }

      .modifiers {
        .clear-filter {
          text-align: right;
          font-size: 12px;

          i[class^="icon-"] {
            margin-right: 2px;
          }
        }

        .expand-all {
          font-size: 12px;

          i[class^="icon-"] {
            margin-right: 2px;
          }
        }
      }

      label {
        font-weight: bold;
        color: #555;
        font-size: 13px;
      }
    }
  }
}
