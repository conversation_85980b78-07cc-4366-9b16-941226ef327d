.questions-form {

  .option {
    margin-bottom: 20px;
  }

  input{
        height: 18px;
        width: auto;
  }

  .settings {
    display: grid;
    grid-template-columns: repeat(3, minmax(200px, 1fr));
    grid-gap: .5rem;

    &.video-question {
      grid-template-columns: repeat(4, minmax(200px, 1fr));
    }

    @media screen and (max-width: 500px) {
      grid-template-columns: auto;
    }

    select {
      width: 240px;
    }

    #id_marks, #id_negative_marks, #id_partial_marks {
      width: 233px;
    }
  }

  .match-type, .table-analysis {
    .question-text, .answer-text {
      input[type=text] {
        width: 90%;
      }
    }

    .delete {
      display: inline-block;
      opacity: 0.2;
      font-weight: bold;
      color: #000;
      text-shadow: 0 1px 0 #ffffff;
      font-size: 20px;
      margin-left: 3px;
      cursor: pointer;
      vertical-align: top;
    }

    #column-a, #column-b, #table-header, #choice-content {
      input[type=text] {
        width: 90%;
      }

      input {
        width: 50%;
      }

      .row-fluid {
        margin-bottom: 20px;

        label {
          font-weight: bold;
          color: #555;
          font-size: 13px;
        }
      }
    }
  }
  
  #answer-keys {
    select {
      text-overflow: ellipsis;
      width: 100%;
    }
    input[type=text] {
      width: 90%;
    }
  }
  
  input[type="submit"].btn {
      height: auto;
  }
}

