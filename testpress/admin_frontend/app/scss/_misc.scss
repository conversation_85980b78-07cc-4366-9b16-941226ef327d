a.danger {
  color: #da4f49 !important;
  margin-left: 5px;
  cursor: pointer;
}

a.mini {
  font-size: 12px;

  [class^="icon-"] {
    margin-right: 3px;
  }
}

.filter-title {
  font-weight: bold;
  vertical-align: top;
  width: auto;
  min-width:100px;
  color:#999999;
  margin-bottom: 5px;
  display: block;
}

.filter-input-box {
  width: 170px;
  text-transform: capitalize;
}

.submit_button {
  margin-top: 10px;
}

.dropdown-header {
  display: block;
  padding: .5rem 1.2rem;
  margin-bottom: 0;
  font-size: 11px;
  color: #333;
  white-space: nowrap;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.03rem;

  [class^="icon-"],
  [class*=" icon-"] {
    margin-right: 3px;
  }
}
.related-item {
  padding: 5px 20px;
}
.related-item:hover {
  background-color: #2f93d7;
  color: #fff;
}

#two-inputs {
  input {
    width: 65px;
  }

  span {
    margin: 0 2px;
    position: relative;
    bottom: 3px;
  }
}
.my-1 {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
}
.py-1{
  padding-top: 0.5em !important;
  padding-bottom: 0.5em !important;
}

.no-border{
  border: none !important;
}

.mb-10 {
  margin-bottom: 10px;
}

.loader-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.lds-ellipsis {
  &.loader {
    left: 0;
  }
}

.modifiers input[type=checkbox] {
  margin-right: 2px;
  margin-top: 3px;
}

.imagepicker {
  z-index: 1001 !important;
}

.fa-spin.loading {
  color: #2f93d7;
  position: relative;
  top: 2px;
}

.text-right.custom {
  padding-bottom: 5px;
}

.uppy-Dashboard--modal .uppy-Dashboard-inner {
    z-index: 1020!important;
}

.custom_icon.empty {
  border-bottom: none;
  text-align: center;
  margin-top: 20%;
  color: #333;

  .title {
    font-size: 18px;
    line-height: 1.8;
  }
}

.highlight {
  background-color: yellow
}

.cursor {
  cursor: pointer;
}

li.empty.empty_content {
  border-bottom: none;
  text-align: center;
  margin-top: 8%;
  list-style-type: none;

  .title {
    font-size: 18px;
    line-height: 1.8;
  }
}

div.empty {
  border-bottom: none;
  text-align: center;
  margin-top: 8%;

  .title {
    font-size: 18px;
    line-height: 1.8;
  }

  button {
    margin-top: 10px;
  }
}

.bold {
  font-weight: bold;
}

.hidden {
  display: none !important;
  visibility: hidden !important;
}

.parallel_login_restriction_fields .sub-heading {
  font-weight: bold;
  font-size: 13px;
  text-transform: uppercase;
  margin: 25px 0 10px;
}

.pure-pagination.pagination {
  display: inline;

  ul {
    box-shadow: none;

    li {
      .disabled {
        color: #aaa;
        text-decoration: none;
        border-top: none;
        border-bottom: none;
        letter-spacing: 5px;

        &:hover {
          text-decoration: none;
          color: #aaa;
        }
      }

      .prev {
        border-bottom: 0;
        border-top: 0;
        border-left: 0;
      }

      .next {
        border: none;
      }

      .goto-page {
        height: 20px;
        border: none;
        padding-left: 0;

        &:hover {
          text-decoration: none;
        }

        form {
          display: inline;
          position: relative;
          bottom: 2px;

          input {
            width: 25px;
            margin-bottom: 2px;
            padding: 2px;

            &[type="text"] {
              padding-left: 5px;
            }

            &[type="submit"] {
              width: auto;
              color: #3598db;
              background: transparent;
              border: none;
            }
          }
        }
      }
    }
  }
}

.post-bin-table {
  td {
    width: 33%;
  }
}
