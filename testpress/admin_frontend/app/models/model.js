module.exports = Backbone.Model.extend({
  url: function() {
    var origUrl = Backbone.Model.prototype.url.call(this);
    return origUrl + (origUrl.charAt(origUrl.length - 1) == '/' ? '' : '/');
  },

  save: function(key, val, options) {
    // Handle both `"key", value` and `{key: value}` -style arguments.
    var attrs;
    if (key == null || typeof key === 'object') {
      attrs = key;
      options = val;
    } else {
      (attrs = {})[key] = val;
    }

    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
    };

    options = options || {};
    if (!options.beforeSend) {
      options['beforeSend'] = sendAuthentication;
    }

    return Backbone.Model.prototype.save.call(this, attrs, options);
  },

  destroy: function(options) {
    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
    };

    options = options || {};
    if (!options.beforeSend) {
      options['beforeSend'] = sendAuthentication;
    }

    return Backbone.Model.prototype.destroy.call(this, options);
  }

});
