var Model = require('./model');

var Course = Model.extend(
{
  urlRoot: '/api/v2.1/courses/'
});

var Courses = Backbone.Collection.extend(
{
  model: Course,
  parse: function (response) {
    this.previous = response.previous;
    this.next = response.next;
    this.perPage = response.per_page;
    this.totalRecords = response.count;
    this.totalPages = Math.ceil(this.totalRecords / this.perPage);
    return response.results;
  }
});

var model = Model.extend(
{
  urlRoot: '/api/v2.5/admin/users/',

  initialize: function() {
    if (!this.isNew()) {
      this.initializeCourses();
    }
    this.listenTo(this, 'sync', this.initializeCourses);
  },

  initializeCourses: function(evt) {
    if (!_.has(this, 'courses')) {
      this.courses = new Courses();
      this.courses.url = this.urlRoot + this.id + '/courses/';
      this.trigger('courses-initialized');
    }
  }
});

module.exports = {
  model: model,
};

