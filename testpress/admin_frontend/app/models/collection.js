import {getQueryVariable} from 'utils/functions';

export default Backbone.Collection.extend({
  parse: function (response) {
    this.previous = response.previous;
    this.next = response.next;
    this.count = response.count;
    this.per_page = response.per_page;
    return response.results;
  },

  save: function(options) {
    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
    };

    options = options || {};
    if (!options.beforeSend) {
      options['beforeSend'] = sendAuthentication;
    }

    Backbone.sync("create", this, options);
  },

  fetchAll: function(data) {
    this.fetch({
      data: data,
      remove: false,
      success: (collection, xhr, options) => {
        if (collection.next !== null) {
          var page = getQueryVariable(collection.next, 'page');
          data['page'] = page;
          this.fetchAll(data);
        } else {
          _.defer(() => {
            this.trigger('fetch-complete', collection, xhr, options);
          });
        }
      },
      error: (collection, xhr, options) => {
        this.trigger('fetch-complete-error', collection, xhr, options);
      }
    });
  }
});
