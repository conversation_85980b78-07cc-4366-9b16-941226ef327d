import Collection from 'models/collection';
import Model from 'models/model';


export var CustomField = Model.extend(
{
  urlRoot: "/api/v2.3/admin/custom_forms/fields/"
});

export var CustomFieldCollection = Collection.extend(
{
  url: "/api/v2.3/admin/custom_forms/fields/",

  model: CustomField,

  comparator: 'order'
});

export var CustomForm = Model.extend(
{
  urlRoot: "/api/v2.3/admin/custom_forms/"
});

export var CustomFormCollection = Collection.extend(
{
  url: "/api/v2.3/admin/custom_forms/",

  model: CustomForm
});

