import Model from './model';
import Collection from './collection';

const SubjectStats = Model.extend(
{
  initialize: function(options) {
    this.userexam_id = options.userexam_id;
  },

  url: function () {
    return "/api/v2.3/attempts/" + this.userexam_id + '/review/subjects/';
  }
});

const SubjectStatsCollection = Collection.extend(
{
  model: SubjectStats,

  initialize: function(models, options) {
    this.userexam_id = options.userexam_id;
  },

  url: function() {
    return "/api/v2.3/attempts/" + this.userexam_id + '/review/subjects/';
  },
});

export {SubjectStatsCollection};
export {SubjectStats}
