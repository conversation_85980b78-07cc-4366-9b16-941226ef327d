import PageableCollection from 'models/paginator';
var Model = require('./model');

var model = Model.extend({
  urlRoot: '/api/v2.3/comments/'
});

var collection = PageableCollection.extend({
  model: model,

  initialize: function(models, options) {
    this.content_object = options.content_object;
  },

  url: function() {
    return this.content_object.get('comments_url');
  },

});

module.exports = {
  model: model,
  collection: collection
};
