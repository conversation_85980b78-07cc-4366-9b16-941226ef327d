import Collection from 'models/collection';
var Model = require('./model');
import {SectionCollection} from 'models/sections';

export var Exam = Model.extend(
{
  urlRoot: "/api/v2.2/admin/exams/",

  validation: {
    title: {
      required: true,
    },
    start_date: {
      required: true,
    }
  },

  initialize: function() {
    if (!this.isNew()) {
      this.updateSections();
    }
    this.listenTo(this, 'sync', this.updateSections);
  },

  updateSections: function() {
    if (!_.has(this, 'sections')) {
      this.sections = new SectionCollection();
      this.sections.url = this.url() + 'sections/';
      this.trigger('sections-added');
    }
  }
});
