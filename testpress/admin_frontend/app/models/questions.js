var Model = require('./model');
import PageableCollection from 'models/paginator';
import {Exam} from 'models/exams';

export var QuestionModel = Model.extend(
{
  urlRoot: '/api/v2.2/admin/questions/',
});

export var QuestionCollection = PageableCollection.extend(
{
  url: '/api/v2.2/admin/questions/',

  model: QuestionModel
});

export var QuestionExamsCollection = PageableCollection.extend({
  model: Exam,

  initialize: function(models, options) {
    this.content_object = options.content_object;
  },

  url: function() {
    return this.content_object.url() + 'exams/';
  },

});
