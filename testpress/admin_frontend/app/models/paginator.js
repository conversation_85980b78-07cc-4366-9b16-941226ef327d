export default Backbone.PageableCollection.extend({
  mode: "server",

  state: {
    firstPage: 1,
    currentPage: 1,
    pageSize: 20
  },

  queryParams: {
    currentPage: "page",
    pageSize: "page_size"
  },

  parseState: function (response, queryParams, state, options) {
    return {totalRecords: response.count};
  },

  parseLinks: function(response, options) {
    return {
      prev: response.previous,
      next: response.next,
      first: null
    }
  },

  parseRecords: function (response, options) {
    this.previous = response.previous;
    this.next = response.next;
    return response.results;
  },

  save: function(options) {
    var sendAuthentication = function(xhr) {
      xhr.setRequestHeader('X-CSRFToken', $.cookie('csrftoken'));
    };

    options = options || {};
    if (!options.beforeSend) {
      options['beforeSend'] = sendAuthentication;
    }

    Backbone.sync("create", this, options);
  }
});
