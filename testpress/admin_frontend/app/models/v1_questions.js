var Collection = require('./collection');
var Model = require('./model');

var model = Model.extend({

  //urlRoot: function() {
  //  return this.question_set.url() + 'questions/' + this.get('id') + '/';
  //},
  //paginator_core: {
  //    type: 'GET',
  //    dataType: 'json',
  //}
    
});

var collection = Backbone.Collection.extend({
  model: model,
  initialize: function(models, options) {
    this.question_set = options.question_set;
    console.log(this.question_set);
  },

  url: function() {
    return this.question_set.url() + 'questions/';
  },
  //paginator_core: {
  //    type: 'GET',
  //    dataType: 'json',
  //},
    
  parse: function(response, options) {
    var resp = Collection.prototype.parse.apply(this, arguments);
    var map = _.map(resp, (function(question) {
     return  _.extend(question, {
        question_set: this.question_set
      });
    }).bind(this));
    return map;
  }
});

module.exports = {
  model: model,
  collection: collection
};

