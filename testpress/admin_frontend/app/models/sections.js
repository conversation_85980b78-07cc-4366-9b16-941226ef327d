import Collection from 'models/collection';
var Model = require('./model');

export var Section = Model.extend(
{
  urlRoot: "/api/v2.2/admin/sections/",

  initialize: function() {
    if (!this.isNew()) {
      this.updateQuestions();
    }
    this.listenTo(this, 'sync', this.updateQuestions);
  },

  updateQuestions: function() {
    if (!_.has(this, 'questions')) {
      this.questions = new Collection();
      this.questions.url = this.url() + 'questions/';
      this.trigger('questions-added');
    }
  }
});

export var SectionCollection = Collection.extend(
{
  model: Section
});
