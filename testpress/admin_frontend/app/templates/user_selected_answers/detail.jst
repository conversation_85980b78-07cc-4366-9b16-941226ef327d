<div class="review-question">
  <div class="pull-left">
    <span class="review-exam-question-no">
      <% if (user_selected_answer.userexam.exam.is_adaptive) { %>
        <%= user_selected_answer.order%>.
      <% } else { %>
      <%= user_selected_answer.order + 1%>. 
      <% } %>
    </span>
  </div>
  <% if (user_selected_answer.question.direction) { %>
    <div style="overflow:hidden" class="custom-pre">
      <%= user_selected_answer.question.direction.html %>
    </div>
  <% } %>
  <div style="overflow:hidden;margin-left: 20px;" class="custom-pre">
    <%= user_selected_answer.question.question_html %>
  </div>
</div> <!-- review-questions --!>
<% if (user_selected_answer.question.type === "M") { %>
  <div class="clearfix">
    <div class="row-fluid match-type-heading">
      <span class="span4">Column A</span>
      <span class="span4">Column B</span>
    </div>
    <div class="row-fluid clearfix" style="margin-left: 37px;">
      <div class="span4 review-exam-question-option">
        <% _.each(user_selected_answer.children, (child, index) => { %>
          <span class="clearfix">
            <%= child.question.question_html %>
          </span>
        <% }) %> <!-- end for loop -->
      </div>
      <div class="span4 review-exam-question-option">
        <% _.each(user_selected_answer.question.answers, (answer, index) => { %>
          <span class="clearfix">
            <%= answer.text_html %>
          </span>
        <% }) %> <!-- end for loop -->
      </div>
    </div>
  </div>
  <table class="match-type-response">
    <div class="re-correct-answer clearfix">
      <span class="re-title">User Response: </span>
    </div>
    <tbody>
      <tr class="heading">
        <td>
          <div>Option</div>
        </td>
        <td>
          <div>User Answer</div>
        </td>
        <td>
          <div>Marks</div>
        </td>
      </tr>
      <% _.each(user_selected_answer.children, (child, index) => { %>
        <tr>
          <td>
            <div>
              <span
              <% if (child.result && child.result.toLowerCase() === "correct") { %>
                  class="btn-success review-exam-option-circle-normal"
              <% } else if (child.result && child.result.toLowerCase() === "incorrect") { %>
                  class="btn-danger review-exam-option-circle-normal"
              <% } else { %>
                  class="review-exam-option-circle-normal"
              <% } %>><%= String.fromCharCode(97 + index) %></span>
              <span class="review-exam-options">
              <p class="clearfix"><%= child.question.question_html %></p>
              </span>
            </div>
          </td>
          <td>
            <span class="">
              <% if (child.selected_answers.length) { %>
                <%= _.find(user_selected_answer.question.answers, (answer) => {
                  return _.contains(child.selected_answers, answer.id)
                }).text_html %>
              <% } else { %>
                 <i>Unanswered</i>
              <% } %>
            </span>
          </td>
          <td>
            <span class="<% if (child.result) { %><%= child.result.toLowerCase() %><% } %>">
              <%= child.marks %>
            </span>
          </td>
        </tr>
      <%})%> <!-- end for loop -->
    </tbody>
  </table>
<% } else if (user_selected_answer.question.type === "G" && user_selected_answer.question.gap_fill_display_type === 4) { %>
  <div class="review-exam-question-option">
    <span class="re-title">Correct Answer:</span>
  </div>
  <% _.each(user_selected_answer.question.answers, (answer, index) => { %>
    <% if (answer.is_correct) { %>
      <div class="review-exam-question-option">
        <span
          <% if (user_selected_answer.ignore && _.contains(user_selected_answer.selected_answers, answer.id)) { %>
            class="btn-warning review-exam-option-circle-normal"
          <% } else if (answer.is_correct && _.contains(user_selected_answer.selected_answers, answer.id)) { %>
              class="btn-success review-exam-option-circle-normal"
          <% } else if (_.contains(user_selected_answer.selected_answers, answer.id)) { %>
              class="btn-danger review-exam-option-circle-normal"
          <% } else { %>
              class="review-exam-option-circle-normal"
          <% } %>><%=  answer.gap_fill_order %></span>
          <span class="review-exam-options">
          <p><%= answer.text_html %></p>
        </span>
      </div>
    <% } %>
  <% }) %>
<% } else if (user_selected_answer.question.type === "T") { %>
  <% _.each(user_selected_answer.children, (child, index) => { %>
    <div class="nested-response">
      <div class="review-question">
        <div class="pull-left">
          <span class="review-exam-question-no"><%= index+1 %>.</span>
        </div>
        <div style="overflow:hidden" class="custom-pre">
          <% if ( child.question.direction) { %>
            <div class="re-question-text clearfix">
              <%= child.question.direction %>
            </div>
          <% } %>
          <div class="re-question-text clearfix">
            <%= child.question.question_html %>
          </div>
        </div>
      </div> <!-- review-question -->
      <% if (child.question.type === "E") { %>
        <% _.each(child.question.essay_topics, (topic, index) => { %>
          <div class="review-exam-question-option">
            <% if (topic.id === child.essay_topic) { %>
              <span class="review-exam-option-circle-normal btn-info">
                <%= String.fromCharCode(97 + index) %>
              </span>
              <span class="review-exam-options">
                <%= topic.title %>
              </span>
            <% } else { %>
              <span class="review-exam-option-circle-normal">
                <%= String.fromCharCode(97 + index) %>
              </span>
              <span class="review-exam-options">
                <%= topic.title %>
              </span>
            <% } %>
          </div>
        <% }) %>
        <div class="review-exam-question-option">
          <span class="re-title">
            <p>Your answer:</p>
          </span>
          <span class="review-exam-options">
            <% if (user_selected_answer.essay_text) { %>
              <%= user_selected_answer.essay_text.replace(/(?:\r\n|\r|\n)/g, '<br/>')%>
            <% } %>
          </span>
        </div>
      <% } else if (child.question.type === "S" || child.question.type === "N") { %>
        <div class="row short-answers-heading">
          <% if (child.question.type === "N") { %>
            <span class="span4">Answer</span>
            <span class="span4" title="For example, if the answer is 30 with an accepted error of 5, then any number between 25 and 35 including 25, 35 will be accepted as correct.">Accepted error
            </span>
          <% } else { %>
            <span class="span4">Answers</span>
            <span class="span4">Marks</span>
          <% } %>
        </div>
        <% _.each(child.question.answers, (answer, index) => { %>
          <div class="row review-exam-question-option">
            <span class="span4 clearfix">
              <%= answer.text_html %>
            </span>
            <% if (user_selected_answer.question.type === "S") { %>
              <span class="span4">
                <%= answer.marks %>%
              </span>
            <% } %>
            <% if (user_selected_answer.question.type === "N") { %>
              <span class="span4">
                <% if (answer.tolerance) { %>
                  <%= answer.tolerance %>
                <% } else { %>
                  0
                <% } %>
              </span>
            <% } %>
          </div>
        <% }) %> <!-- end for loop -->
      <% } else if (child.question.type === "F") { %>
        <div class="row short-answers-heading">
          <span class="span4">Uploaded Files</span>
        </div>
        <% _.each(user_selected_answer.files, (file, index) => { %>
          <div class="row review-exam-question-option">
            <span class="span4">
              <a href="<%= file.url %>" target="_blank">File <%= index + 1 %></a>
            </span>
          </div>
        <% }) %> <!-- end for loop -->
      <% } else { %>
        <% _.each(child.question.answers, (answer, index) => { %>
          <div class="review-exam-question-option">
            <span
            <% if (child.ignore && _.contains(child.selected_answers, answer.id)) { %>
                class="btn-warning review-exam-option-circle-normal"
            <% } else if (answer.is_correct &&
                _.contains(child.selected_answers, answer.id)) { %>
                class="btn-success review-exam-option-circle-normal"
            <% } else if (_.contains(child.selected_answers, answer.id)) { %>
                class="btn-danger review-exam-option-circle-normal"
            <% } else { %>
                class="review-exam-option-circle-normal"
            <% } %>><%= String.fromCharCode(97 + index) %></span>
            <span class="review-exam-options clearfix">
              <p><%= answer.text_html %></p>
            </span>
          </div>
        <% }) %> <!-- end for loop -->
      <% } %> <!-- end if -->
    </div>
  <% }) %>
<% } else if (user_selected_answer.question.type == "A") { %>
  <div>
    <% _.each(user_selected_answer.files, (file, index) => { %>
      <div class="row-fluid review-exam-question-option">
        <span class="span4">
          <audio src="<%= file.url %>" controls></audio>
        </span>
      </div>
    <% }) %>
  </div>
<% } else if (user_selected_answer.question.type == "F") { %>
  <div class="row-fluid short-answers-heading">
    <span class="span4">Uploaded files</span>
  </div>
  <% _.each(user_selected_answer.files, (file, index) => { %>
    <div class="row-fluid review-exam-question-option">
      <span class="span4">
        <a href="<%= file.url %>" target="_blank">File <%= index + 1 %></a>
      </span>
    </div>
  <% }) %>
<% } else if (user_selected_answer.question.type == "S" || user_selected_answer.question.type == "N") { %>
  <% if (user_selected_answer.question.type == "S") { %>
    <div class="row-fluid short-answers-heading">
      <span class="span4">Answers</span>
      <span class="span4">Marks</span>
    </div>
  <% } else { %>
    <div class="row-fluid short-answers-heading">
      <span class="span4">Answer</span>
      <span class="span4" title="For example, if the answer is 30 with an accepted error of 5, then any number between 25 and 35 including 25, 35 will be accepted as correct.">Accepted error</span>
    </div>
  <% } %>
  <% _.each(user_selected_answer.question.answers, (answer) => { %>
    <div class="row-fluid review-exam-question-option">
      <span class="span4">
        <%= answer.text_html %>
      </span>
      <% if (user_selected_answer.question.type == "S") { %>
        <span class="span4">
          <%= answer.marks %>%
        </span>
      <% } %>
      <% if (user_selected_answer.question.type == "N") { %>
        <span class="span4">
          <%= answer.tolerance %>
        </span>
      <% } %>
    </div>
  <% }) %>
<% } else if (user_selected_answer.question.type == "E") { %>
  <% _.each(user_selected_answer.question.topics, (topic, index) => { %>
    <% if (user_selected_answer.essay_topic.id == topic.id) { %>
      <div class="review-exam-question-option">
        <span class="btn-info review-exam-option-circle-normal">
          <%= index %>
        </span>
        <span class="review-exam-options">
          <%= topic.title %>
        </span>
      </div>
    <% } else { %>
      <div class="review-exam-question-option">
        <span class="review-exam-option-circle-normal">
          <%= index %>
        </span>
        <span class="review-exam-options">
          <%= topic.title %>
        </span>
      </div>
    <% } %>
  <% }) %>
  <div class="review-exam-question-option">
    <div class="re-title">Given Answer: </div>
    <% if (user_selected_answer.essay_text) { %>
      <span><%= user_selected_answer.essay_text.replace(/(?:\r\n|\r|\n)/g, '<br/>') %></span>
    <% } %>
  </div>
  <div class="review-explanation clearfix">
    <span class="re-title">Word Count: </span>
    <p class="re-subject"><%= $.trim(user_selected_answer.essay_text).length ? user_selected_answer.essay_text.match(/\S+/g).length : 0 %></p>
  </div>
<% } else { %>
  <% _.each((user_selected_answer.question.answers), (answer, index) => { %>
  <div class="review-exam-question-option">
    <span
      <% if (user_selected_answer.ignore && _.contains(user_selected_answer.selected_answers, answer.id)) { %>
        class="btn-warning review-exam-option-circle-normal"
      <% } else if (answer.is_correct && _.contains(user_selected_answer.selected_answers, answer.id)) { %>
          class="btn-success review-exam-option-circle-normal"
      <% } else if (_.contains(user_selected_answer.selected_answers, answer.id)) { %>
          class="btn-danger review-exam-option-circle-normal"
      <% } else { %>
          class="review-exam-option-circle-normal"
      <% } %>><%= String.fromCharCode(97 + index) %></span>
      <span class="review-exam-options">
      <p><%= answer.text_html %></p>
    </span>
  </div>
  <% }) %>
<% } %>
<div class="review-explanation clearfix" style="margin-top: 20px;">

  <% if (user_selected_answer.question.explanation_html) { %>
    <div>
      <span class="re-title">Explanation: </span>
      <%= user_selected_answer.question.explanation_html %>
    </div>
  <% } %>

  <% if (user_selected_answer.question.type == 'S' || user_selected_answer.question.type == 'N') { %>
    <div class="re-subject-row">
      <span class="re-title">User Answer: </span>
      <span><%= user_selected_answer.short_text %></span><br>
    </div>
  <% } %>
  <div class="re-subject-row">
    <span class="re-title">Marks Awarded: </span>
    <p class="re-subject"><%= user_selected_answer.marks %></p><br>
  </div>
  
  <% if (user_selected_answer.question.marks) { %>
  <div class="re-subject-row">
    <span class="re-title">Marks: </span>
    <p class="re-subject"><%= user_selected_answer.question.marks %></p><br>
  </div><!-- re-subject -->
  <% } %>

  <% if (user_selected_answer.question.negative_marks) { %>
  <div class="re-subject-row">
    <span class="re-title">Negative Marks: </span>
    <p class="re-subject"><%= user_selected_answer.question.negative_marks %></p><br>
  </div><!-- re-subject -->
  <% } %>

  <% if (user_selected_answer.question.difficulty_level) { %>
  <div class="re-subject-row clearfix">
    <span class="re-title">Difficulty Level: </span>
    <span><%= user_selected_answer.question.difficulty_level_string %></span><br>
  </div><!-- re-difficulty_level -->
  <% } %>
  
  <% if (user_selected_answer.question.subject) { %>
    <div class="re-subject-row">
      <span class="re-title">Subject: </span>
      <p class="re-subject"><%= user_selected_answer.question.subject.name %></p><br>
    </div>
  <% } %>
<% if (user_selected_answer.question.percentage_got_correct) { %>
  <div class="re-subject-row">
    <span class="re-title">Trivia: </span>
    <p>
      <span class="percentage"><%=user_selected_answer.question.percentage_got_correct %>% </span>
      <span class="text">users answered right</span>
    </p>
  </div>
<% } %>
<% if (user_selected_answer.question.type !== 'E' &&
        user_selected_answer.question.type !== 'S' &&
        user_selected_answer.question.type !== 'F' &&
        user_selected_answer.question.type !== 'N' &&
        user_selected_answer.question.type !== 'T' &&
        user_selected_answer.question.type !== 'G' &&
        user_selected_answer.question.type !== 'M') { %>
    <div class="re-subject-row">
      <span class="re-title">Correct Answer: </span>
        <% _.each(user_selected_answer.question.answers, (answer, index) => { %>
          <% if (answer.is_correct) { %>
            <span class="review-exam-option-circle-normal btn-info">
              <%= String.fromCharCode(97 + index) %>
            </span>
          <% } %>
        <% }) %>
      </span>
    </div>
      <br>
  <%} else if (user_selected_answer.question.type === 'T'){%>
    <div class="re-subject-row">
      <span class="re-title">Correct Answers: </span>
      <% _.each(user_selected_answer.children, function(child, index){ %>
        <% _.each(child.question.answers, function(answer, index){ %>
          <% if (answer.is_correct) { %>
            <span class="review-exam-option-circle-normal btn-info">
              <%= String.fromCharCode(97 + index) %>
            </span>
          <% } %>
        <%})%> <!-- end for loop -->
      <%})%> <!-- end for loop -->
    </div>
  <%} else if (user_selected_answer.question.type === 'M'){%>
    <div class="re-subject-row">
      <span class="re-title">Correct Answer: </span>
      <table class="match-type-response correct">
        <tbody>
          <% _.each(user_selected_answer.children, (child, index) => { %>
            <tr>
              <td>
                <div class="review-exam-question-option">
                  <span class="review-exam-options clearfix">
                  <p><%= child.question.question_html %></p>
                  </span>
                </div>
              </td>
              <td>
                <span class="clearfix">
                  <%= _.find(user_selected_answer.question.answers, (answer) => {
                    return child.question.id == answer.is_correct_for_id;
                  }).text_html %>
                </span>
              </td>
            </tr>
          <%})%> <!-- end for loop -->
        </tbody>
      </table>
    </div>
  <% } else if (user_selected_answer.question.type == "S") { %>
    <div class="re-subject-row">
      <span class="re-title">Note: </span>
      <p class="re-subject">
        Answers are
        <% if (user_selected_answer.question.is_case_sensitive) { %>
          Case sensitive,
        <% } else { %>
          Case insensitive,
        <%}%>
        <% if(user_selected_answer.question.is_case_sensitive) { %>
        <span>You have marked this for review and skipped it so this won't be considered for evaluation.</span>
        <% } %>
      </p>
    </div>
  <% } %>
</div><!-- review-explanation clearfix -->
<% if (["F", "A", "E"].indexOf(user_selected_answer.question.type) > -1) { %>
  <div class="feedback-comments">
    <div class="action">
      <a class="give-feedback" href="#">
        <i class="icon-comments"></i>
        <span>Post feedback <% if (feedback_count) { %>(<%= feedback_count %>)<% } %></span>
      </a>
    </div>
  </div>
  <div class="grading">
    <div class="action">
      <div class="icon"><i class="icon-pencil"></i></div>
      <div class="grade-label">
        Grade:
      </div>
      <a class="grade" href="#"></a>
    </div>
  </div>
  <div class="discussions comment hidden">
    <ul class="media-list"></ul>
    <div class="reply"></div>
  </div>
<% } %>
