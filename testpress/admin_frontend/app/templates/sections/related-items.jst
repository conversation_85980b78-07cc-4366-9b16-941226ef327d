<li class="loading-related hidden">
  <div class="text-center">
    <i class="icon-spinner icon-spin"></i> Loading...
  </div>
</li>
<li class="question-sets-container">
  <% if (questionSet) { %>
    <div class="dropdown-header"><i class="icon-question-sign"></i> Question Sets</div>
    <div class="question-sets-list related-item">
      <a href="/set/<%= questionSet.id %>/questions" data-id="<%= questionSet.id %>" style="text-decoration: none; color: inherit;">
        <%= questionSet.title %>
      </a>
    </div>
  <% } %>
</li>
<li class="exams-container">
    <% if (exams && exams.length) { %>
      <div class="dropdown-header"><i class="icon-signup"></i> Exams</div>
      <div class="exams-list">
      <% _.each(exams, function(exam) { %>
        <div class="related-item">
          <a href="/exams/<%= exam.id %>/update/" data-id="<%= exam.id %>" style="text-decoration: none; color: inherit;">
            <%= exam.title %>
          </a>
        </div>
      <% }); %>
      </div>
    <% } else { %>
      <div class="dropdown-header"><i class="icon-signup"></i> No Exams</div>
    <% } %>
</li>