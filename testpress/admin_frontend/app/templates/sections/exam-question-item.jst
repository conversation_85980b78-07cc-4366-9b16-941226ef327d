  <div class="index">
    <%= order %>.
    <input name="exam_questions" value="<%= id %>" type="checkbox"/>
  </div>
  <div class="main">
    <div class="content">
      <div class="short">
        <span class="label label-info"><%= question.type_string %> Question</span>
        <div class="question"><%= question.question_html %></div>
        <a class="more" href="#">More</a>
      </div><!-- short -->
      <div class="long hidden">
        <span class="label label-info"><%= question.type_string %> Question</span>
        <% if (question.direction) { %>
        <div class="short-passage">
          <%= question.direction.short_html %>
        </div>
        <div class="long-passage hidden">
          <%= question.direction.html %>
        </div>
       <a class="more-passage" href="#" data-toggle="popover" title="" data-html="<%= question.direction.html %>" >Show Complete Passage</a> 
        <% } %>
        <div class="question-html"><%= question.question_html %></div>
        <div class="choices clearfix">
          <% _.each(question.answers, function(answer, index){ %>
          <div class="choice">
            <div class="answer-ring <% if (answer.is_correct) {%> answer-ring-correct<% } %>"><span class="answer-option"><%= String.fromCharCode(97 + index) %></span></div>
            <div class="pull-left">
            <%= answer.text_html %>
            </div>
          </div>
          <% }) %>
        </div>
        <a class="less" href="#">Less</a>
      </div><!-- .long -->
    </div><!-- .content -->
    <div class="actions">
  	<!-- <a href="#" data-id="{{ examquestion.id }}" class="btn btn-mini pull-right"><i class="icon-edit"></i>Edit</a> -->
      <a href="#" data-id="<%= id %>" class="danger mini"><i class="icon-remove-sign"></i>Delete</a>
    </div>
  </div><!-- .main -->
