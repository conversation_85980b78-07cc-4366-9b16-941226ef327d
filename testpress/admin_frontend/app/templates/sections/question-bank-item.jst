<div class="index">
  <input name="questions" value="<%= id %>" type="checkbox"/>
</div><!-- div .index -->
<div class="main">
  <div class="content">
    <div class="short">
      <div class="row-fluid">
        <span class="span8">
          <span class="label label-info pull-left"><%= type_string %> Question</span>
        </span>
        <% if (type === 'S') { %>
          <span class="span4 short-answers-heading">
            <% if (is_case_sensitive) { %>
              <span class="label label-warning pull-right">Case-sensitive</span>
            <% } else { %>
              <span class="label label-warning pull-right">Case-insensitive</span>
            <% } %>
          </span>
        <% } %>
      </div>
      <div class="question clearfix"><%= question_html %></div>
      <a class="more" href="#">More</a>
    </div><!-- short -->
    <div class="long hidden">
      <div class="row-fluid">
        <span class="span8">
          <span class="label label-info pull-left"><%= type_string %> Question</span>
        </span>
        <% if (type === 'S') { %>
          <span class="span4 short-answers-heading">
            <% if (is_case_sensitive) { %>
              <span class="label label-warning pull-right">Case-sensitive</span>
            <% } else { %>
              <span class="label label-warning pull-right">Case-insensitive</span>
            <% } %>
          </span>
        <% } %>
      </div>
      <% if (direction) { %>
      <div class="short-passage clearfix">
        <%= direction.short_html %>
      </div>
      <div class="long-passage clearfix hidden">
        <%= direction.html %>
      </div>
     <a class="more-passage" href="#" data-toggle="popover" title="" >Show Complete Passage</a> 
      <% } %>
      <div class="question-html clearfix"><%= question_html %></div>
      <% if (type === 'S' || type === 'N') { %>
        <div class="row-fluid short-answers-heading">
          <% if (type === 'S') { %>
            <span class="span8">
              <span class="label short-answer-info pull-left">Answers</span>
            </span>
            <span class="span4">
              <span class="label short-answer-info pull-right">Marks</span>
            </span>
          <% } else { %>
            <span class="span8">
              <span class="label short-answer-info pull-left">Answer</span>
            </span>
            <span class="span4">
              <span class="label short-answer-info pull-right" title="For example, if the answer is 30 with an accepted error of 5, then any number between 25 and 35 including 25, 35 will be accepted as correct.">Accepted error</span>
            </span>
          <% } %>
        </div>
        <% _.each(answers, (answer, index) => { %>
          <div class="row-fluid short-answers">
            <div class="span8 clearfix">
              <span class="pull-left"><%= answer.text_html %></span>
            </div>
            <% if (type === 'S') { %>
              <div class="span4">
                <span class="pull-right"><%= answer.marks %>%</span>
              </div>
            <% } %>
            <% if (type === 'N') { %>
              <div class="span4">
                <span class="pull-right"><% if (answer.tolerance) { %><%=
                answer.tolerance %><% } else { %>0<% } %></span>
              </div>
            <% } %>
          </div>
        <% }) %>
      <% } else if (type === "M") { %>
        <div class="clearfix">
          <div class="row-fluid short-answers-heading">
            <span class="span4">
              <span class="label short-answer-info">Column A</span>
            </span>
            <span class="span4">
              <span class="label short-answer-info">Column B</span>
            </span>
          </div>
          <div class="row-fluid clearfix">
            <div class="span4 review-exam-question-option">
              <% _.each(children, (child, index) => { %>
                <div class="option">
                  <%= child.question_html %>
                </div>
              <% }) %> <!-- end for loop -->  
            </div>
            <div class="span4 review-exam-question-option">
              <% _.each(answers, (answer, index) => { %>
                <div class="option">
                  <%= answer.text_html %>
                </div>
              <% }) %> <!-- end for loop -->  
            </div>
          </div>
          <div>
            <div class="heading">
              Correct Answer:
            </div>
            <div>
              <% _.each(answers, (answer, index) => { %>
                <div class="option">
                  <%= _.find(children, (child) => {
                    return (child.id == answer.is_correct_for)
                  }).question_html %>
                  ----
                  <%= answer.text_html %>
                </div>
              <% }) %> <!-- end for loop -->  
            </div>
          </div>
        </div>
     <% } else if (type === "T") { %>
       <% _.each(children, (child, index) => { %>
         <div class="nested-response">
           <div class="row-fluid">
             <span class="span8">
               <span class="label label-info pull-left"><%= child.type_string %> Question</span>
             </span>
             <% if (child.type === 'S') { %>
               <span class="span4 short-answers-heading">
                 <% if (child.is_case_sensitive) { %>
                   <span class="label label-warning pull-right">Case-sensitive</span>
                 <% } else { %>
                   <span class="label label-warning pull-right">Case-insensitive</span>
                 <% } %>
               </span>
             <% } %>
           </div>
           <% if (child.direction) { %>
           <div class="short-passage clearfix">
             <%= child.direction.short_html %>
           </div>
           <div class="long-passage clearfix hidden">
             <%= child.direction.html %>
           </div>
           <a class="more-passage" href="#" data-toggle="popover" title="" >Show Complete Passage</a> 
           <% } %>
           <div class="question-html clearfix"><%= child.question_html %></div>
           <% if (child.type === 'S' || child.type === 'N') { %>
             <div class="row-fluid short-answers-heading">
               <% if (child.type === 'S') { %>
                 <span class="span8">
                   <span class="label short-answer-info pull-left">Answers</span>
                 </span>
                 <span class="span4">
                   <span class="label short-answer-info pull-right">Marks</span>
                 </span>
               <% } else { %>
                 <span class="span8">
                   <span class="label short-answer-info pull-left">Answer</span>
                 </span>
                 <span class="span4">
                   <span class="label short-answer-info pull-right" title="For example, if the answer is 30 with an accepted error of 5, then any number between 25 and 35 including 25, 35 will be accepted as correct.">Accepted error</span>
                 </span>
               <% } %>
             </div>
             <% _.each(child.answers, (answer, index) => { %>
               <div class="row-fluid short-answers">
                 <div class="span8 clearfix">
                   <span class="pull-left"><%= answer.text_html %></span>
                 </div>
                 <% if (child.type === 'S') { %>
                   <div class="span4">
                     <span class="pull-right"><%= answer.marks %>%</span>
                   </div>
                 <% } %>
                 <% if (child.type === 'N') { %>
                   <div class="span4">
                     <span class="pull-right"><% if (answer.tolerance) { %><%=
                     answer.tolerance %><% } else { %>0<% } %></span>
                   </div>
                 <% } %>
               </div>
             <% }) %>
           <% } else { %>
             <div class="choices clearfix">
               <% _.each(child.answers, function(answer, index){ %>
               <div class="choice clearfix">
                 <div class="answer-ring <% if (answer.is_correct) {%> answer-ring-correct<% } %>"><span class="answer-option"><%= String.fromCharCode(97 + index) %></span></div>
                 <div class="pull-left">
                 <%= answer.text_html %>
                 </div>
               </div>
               <% }) %>
             </div>
           <% } %>
         </div>
       <% }) %>
      <% } else { %>
        <div class="choices clearfix">
          <% _.each(answers, function(answer, index){ %>
          <div class="choice clearfix">
            <div class="answer-ring <% if (answer.is_correct) {%> answer-ring-correct<% } %>"><span class="answer-option"><%= String.fromCharCode(97 + index) %></span></div>
            <div class="pull-left">
            <%= answer.text_html %>
            </div>
          </div>
          <% }) %>
        </div>
      <% } %>
      <% if (explanation_html) { %>
        <div class="explanation">
           <div class="heading">Explanation</div>
           <div class="content"><%= explanation_html %></div>
        </div>
      <% } %>
      <% if (subject) { %>
        <div class="subject">
           <div class="heading">Subject</div>
           <div class="content"><%= subject.tree_path %></div>
        </div>
      <% } %>
      <% if (difficulty_level) { %>
        <div class="difficulty_level">
           <div class="heading">Difficulty Level</div>
           <div class="content"><%= difficulty_level_string %></div>
        </div>
      <% } %>
      <a class="less" href="#">Less</a>
    </div><!-- .long -->
    <% if (percentage_got_correct) { %>
      <strong><%= percentage_got_correct %>%</strong> of attempted got correct.
    <% } %>
  </div><!-- .content -->
  <div class="actions">
    <a href="/admin/questions/<%= id %>/" target="_blank" class="mini" data-original-title="" title=""><i class="icon-edit"></i>Edit Question</a>
   
    <span class="dropdown" id="related-<%= id %>">
      <a class="mini dropdown-toggle" data-toggle="dropdown" data-target="#related-<%= id %>" href="#">
        <i class="icon-exchange"></i> View Related
      </a>
      <ul class="dropdown-menu">
      </ul>
    </span>
  <!-- 
    <span class="dropdown" id="related-7"><a class="mini" data-toggle="dropdown" data-target="#related-7" href="#" data-original-title="" title=""><i class="icon-exchange"></i>View Related</a>
      <ul class="dropdown-menu">
        
        <li class="dropdown-header"><i class="icon-question-sign"></i>Question Set</li>
        <li>
          <a href="/set/1/questions/?examquestion=7" data-id="7" data-original-title="" title="">Match Type Questions</a>
        </li>
        
        <li class="dropdown-header"><i class="icon-signup"></i>Exams</li>
        
        <li>
          <a href="/exams/2/update/?examquestion=7" data-id="7" data-original-title="" title="">Match Type Exam</a>
        </li>
        
      </ul>
    </span>
    <a href="#" style="margin-left: 10px" data-id="7" class="danger mini" data-original-title="" title=""><i class="icon-remove-sign"></i>Delete</a> -->
  </div> <!-- .actions -->
</div><!-- .main -->
