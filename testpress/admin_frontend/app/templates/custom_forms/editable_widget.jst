<div class="span7">
  <% if (field_type == 15) { %>
    <div class="control-group">
      <div class="controls">
        <label class="control-label">Field Label</label>
        <textarea id="<%= formElementId %>" name="label" value="<%= label %>"></textarea>
      </div>
    </div>
  <% } else { %>
    <div class="control-group">
      <div class="controls">
        <label class="control-label">Field Label</label>
        <input type="text" name="label" value="<%= label %>" placeholder="Name of the field" class="input-xlarge">
      </div>
    </div>
  <% } %>
  <div class="control-group">
    <div class="controls choices">
    </div>
  </div>
</div>
<div class="field_type span4">
  <div class="control-group">
    <label class="control-label">Field Type</label>
    <select name='field_type' id="field_type_select">
      <option value="1">File</option>
      <option value="2">Photo</option>
      <option value="3">Number</option>
      <option value="4">Decimal</option>
      <option value="5">Short Text</option>
      <option value="6">Paragraph Text</option>
      <option value="7">Radio</option>
      <option value="8">Checkbox</option>
      <option value="9">Dropdown</option>
      <option value="10">Multiple Choice</option>
      <option value="11">Date</option>
      <option value="12">Time</option>
      <option value="13">Email</option>
      <option value="14">Website</option>
      <option value="15">Decision Box</option>
    </select>
  </div>

  <div class="control-group">
    <% if (form_type == 1) {%>
      <div class="control-group" style="margin-bottom: 10px;" id='display_location_container'>
        <label class="control-label">Display Location</label>
        <select multiple='true' id='display_location_select'>
          <option value="1" <% if (_.contains(display_location, 1)) {%>selected<% } %> >Registration form</option>
          <option value="2" <% if (_.contains(display_location, 2)) {%>selected<% } %> >User update/force update form</option>
        </select>
      </div>
      <label class="checkbox is_hidden_container" data-toggle="tooltip" data-placement="bottom" title="If selected this field will be available for admins only">
        <input class="is_hidden" type="checkbox" <% if (is_hidden) { %>checked<% } %> > Is Hidden?
      </label>
    <% } %>
    <label class="checkbox is_required_container" data-toggle="tooltip" data-placement="bottom" title="If selected this field will become mandatory">
      <input class="is_required" type="checkbox" <% if (is_required) { %>checked<% } %> > Is Required?
    </label>
    <label class="checkbox unique hidden" data-toggle="tooltip" data-placement="bottom" title="If selected this field won't accept duplicate values">
      <input type="checkbox" name="is_unique" <% if (is_unique) { %>checked<% } %> > Is Unique?
    </label>
  </div>
</div>
