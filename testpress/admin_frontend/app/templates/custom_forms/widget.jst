
<div class="control-group">
  <label class="control-label <% if (is_required) { %> is-required <% } %>"><%= label %></label>
  <div class="controls">
  <% if (field_type == 1 || field_type == 2) { %>
    <input disabled class="input-file" type="file">
  <% } else if (field_type == 3) { %>
    <input disabled type="number" class="input-xlarge">
  <% } else if (field_type == 4) { %>
    <input disabled type="number" step=".01" class="input-xlarge">
  <% } else if (field_type == 5) { %>
    <input disabled type="text" class="input-xlarge">
  <% } else if (field_type == 6) { %>
    <textarea class="input-xlarge"></textarea>
  <% } else if (field_type == 7) { %>
    <% _.each(field_choices, function(choice, index) { %>
      <label class="radio">
        <input disabled type="radio" name="radio-<%= cid %>" value="<%= choice %>">
        <%= choice %>
      </label>
    <% }) %>
  <% } else if (field_type == 8) { %>
    <% _.each(field_choices, function(choice, index) { %>
      <label class="checkbox">
        <input disabled type="checkbox" name="checkbox-<%= cid %>" value="<%= choice %>">
        <%= choice %>
      </label>
    <% }) %>
  <% } else if (field_type == 9) { %>
    <select class="input-xlarge">
    <% _.each(field_choices, function(choice, index) { %>
        <option value="<%= choice %>">
        <%= choice %>
        </option>
    <% }) %>
    </select>
  <% } else if (field_type == 10) { %>
    <select multiple class="input-xlarge">
    <% _.each(field_choices, function(choice, index) { %>
        <option value="<%= choice %>">
        <%= choice %>
        </option>
    <% }) %>
    </select>
  <% } else if (field_type == 11) { %>
    <input disabled type="date" class="input-xlarge">
  <% } else if (field_type == 12) { %>
    <input disabled type="time" class="input-xlarge">
  <% } else if (field_type == 13) { %>
    <input disabled type="email" class="input-xlarge">
  <% } else if (field_type == 14) { %>
    <input disabled type="url" class="input-xlarge">
  <% } %>
  </div>
</div> 
