from django.db import models
from model_utils.models import TimeStampedModel
from apps.utils.fields import TenantStorageFileField
from apps.utils.misc import institute_upload_path
from apps.utils.models import StorageVendorModel
from testpress.files.storages import TenantPublicStorage


class DesktopApplication(TimeStampedModel, StorageVendorModel):
    name = models.CharField(max_length=255)
    version = models.CharField(max_length=50)
    institute = models.OneToOneField(
        "institutes.Institute", related_name="desktop_application", on_delete=models.CASCADE
    )
    microsoft_store_product_id = models.Char<PERSON><PERSON>(max_length=255, unique=True)
    apple_app_store_product_id = models.Char<PERSON>ield(max_length=255, unique=True)
    windows_installer_file = TenantStorageFileField(
        upload_to=institute_upload_path,
        null=True,
        blank=True,
        max_length=2048,
        storage_class=TenantPublicStorage,
    )
    mac_installer_file = TenantStorageFileField(
        upload_to=institute_upload_path,
        null=True,
        blank=True,
        max_length=2048,
        storage_class=TenantPublicStorage,
    )

    def __str__(self):
        return f"{self.name} ({self.version})"
